import { NgIf } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { ContentDialog } from '../../../model/choice.model';

@Component({
  selector: 'app-dialog-popup',
  standalone: true,
  imports: [
    DialogModule,
    NgIf
  ],
  templateUrl: './dialog-popup.component.html',
  styleUrl: './dialog-popup.component.scss'
})
export class DialogPopupComponent {
  @Input() visible: boolean = false;
  @Input() textData: ContentDialog | any



  @Output() visibleChange = new EventEmitter<boolean>()

  @Output() actionButtonLeft = new EventEmitter()
  @Output() actionButtonRight = new EventEmitter()

  onHide() {
    this.visibleChange.emit(false);
  }

  getClosableValue(): boolean {
    const result = this.textData?.showCloseButton || false;
    console.log('Dialog closable value:', result, 'textData:', this.textData);
    return result;
  }

  getDialogStyleClass(): string {
    // 如果需要顯示關閉按鈕，添加特殊的樣式類
    return this.textData?.showCloseButton ? 'dialog-with-close-button' : '';
  }
}
