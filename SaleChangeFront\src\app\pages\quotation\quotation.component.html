<div class="wrapper">
  <div class="content">

    <!-- 載入狀態 -->
    <div *ngIf="isLoading()" class="loading-container">
      <div class="loading-spinner">載入中...</div>
    </div>

    <!-- 錯誤狀態 -->
    <div *ngIf="error()" class="error-container">
      <div class="error-message">
        <i class="icon-alert"></i>
        {{ error() }}
      </div>
      <button class="btn btn-primary" (click)="loadQuotationHistory()">重新載入</button>
    </div>

    <!-- 主要內容 - 只有在沒有載入且沒有錯誤時顯示 -->
    <div *ngIf="!isLoading() && !error()">

      <!-- 頁面標題 - 簡化 -->
      <div class="page-title">報價單管理</div>

      <!-- 無報價單時的空狀態 -->
      <div *ngIf="quotationVersions().length === 0" class="empty-state">
        <div class="empty-state-content">
          <div class="empty-icon">
            <i class="icon-file-text"></i>
          </div>
          <h3 class="empty-title">暫無報價單記錄</h3>
          <p class="empty-description">
            目前系統中沒有任何報價單，<br>
            請確認是否有報價單資料或嘗試重新載入
          </p>
          <div class="empty-actions">
            <button class="btn btn-primary" (click)="loadQuotationHistory()">
              <i class="icon-refresh-cw"></i>
              重新載入資料
            </button>
          </div>
        </div>
      </div>

      <!-- 有報價單時顯示的內容 -->
      <div *ngIf="quotationVersions().length > 0">
        <!-- 緊湊型報價單頭部區域 -->
        <div class="quotation-header-compact">
          <div class="quotation-summary-card">
            <!-- 基本資訊 - 水平排列 -->
            <div class="basic-info-row" *ngIf="selectedVersion()">
              <div class="info-group">
                <span class="info-label">
                  <i class="pi pi-file"></i>
                  報價單號
                </span>
                <span class="info-value">{{ selectedVersion()!.CversionNo }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">
                  <i class="pi pi-info-circle"></i>
                  狀態
                </span>
                <span class="info-value">
                  <span class="compact" [ngClass]="getStatusClass(selectedVersion()!)">
                    {{ getStatusText(selectedVersion()!) }}
                  </span>
                </span>
              </div>
              <div class="info-group">
                <span class="info-label">
                  <i class="pi pi-calendar"></i>
                  建立日期
                </span>
                <span class="info-value">{{ getCreateDate(selectedVersion()!) | date:'yyyy/MM/dd' }}</span>
              </div>
              <div class="info-group amount-group">
                <span class="info-label">
                  <i class="pi pi-dollar"></i>
                  總金額
                </span>
                <span class="info-value amount">NT$ {{ selectedVersion()!.CTotalAmount | number:'1.0-0' }}</span>
              </div>
            </div>

            <!-- 操作按鈕 - 水平排列 -->
            <div class="action-buttons-row">
              <button class="btn-compact btn-outline" (click)="toggleVersionHistory()">
                <i class="pi pi-history"></i>
                <span>版本歷程</span>
              </button>
              <button class="btn-compact btn-primary" (click)="printPreview()">
                <i class="pi pi-print"></i>
                <span>預覽列印</span>
              </button>
              <button class="btn-compact btn-warning" (click)="openSignatureDialog()"
                *ngIf="selectedVersion() && convertStatusFromAPI(selectedVersion()!.CQuotationStatus) !== 'confirmed'">
                <i class="pi pi-pencil"></i>
                <span>線上簽署</span>
              </button>
              <button class="btn-compact btn-success" (click)="viewContract()"
                *ngIf="selectedVersion() && convertStatusFromAPI(selectedVersion()!.CQuotationStatus) === 'confirmed'">
                <i class="pi pi-file"></i>
                <span>查看合約</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 版本歷程 Dialog -->
        <app-dialog-popup [(visible)]="showVersionHistory" [textData]="versionHistoryTextData">

          <div class="version-dialog-content">
            <!-- 載入狀態 -->
            <div *ngIf="isLoading()" class="dialog-loading">
              <div class="loading-spinner">
                <i class="icon-loader"></i>
                <span>載入版本資料中...</span>
              </div>
            </div>

            <!-- 錯誤狀態 -->
            <div *ngIf="error()" class="dialog-error">
              <div class="error-content">
                <i class="icon-alert-triangle"></i>
                <span class="error-text">{{ error() }}</span>
                <button class="btn btn-primary" (click)="loadQuotationHistory()">
                  <i class="icon-refresh-cw"></i>
                  重新載入
                </button>
              </div>
            </div>

            <!-- 空狀態 -->
            <div *ngIf="!isLoading() && !error() && quotationVersions().length === 0" class="dialog-empty">
              <div class="empty-content">
                <i class="icon-file-text"></i>
                <span>暫無版本記錄</span>
              </div>
            </div>

            <!-- 版本清單 - 重新設計UI -->
            <div *ngIf="!isLoading() && !error() && quotationVersions().length > 0" class="version-list-simple">
              <!-- 版本清單標題 -->
              <div class="version-list-header">
                <h4 class="version-list-title">
                  <i class="pi pi-list"></i>
                  版本清單
                </h4>
                <div class="version-count">
                  共 {{ quotationVersions().length }} 個版本
                </div>
              </div>

              <!-- 欄位標題 -->
              <div class="version-table-header">
                <div class="header-column quotation-number-col">
                  <i class="pi pi-file"></i>
                  <span>報價單號</span>
                </div>
                <div class="header-column create-date-col">
                  <i class="pi pi-calendar"></i>
                  <span>建立日期</span>
                </div>
                <div class="header-column select-col">
                  <span>選擇</span>
                </div>
              </div>

              <!-- 版本項目 -->
              <div class="version-item-simple" *ngFor="let version of quotationVersions(); let i = index"
                [class.selected]="selectedVersion() === version" (click)="selectVersion(version)">

                <div class="version-column quotation-number-col">
                  <span class="quotation-number">{{ version.CversionNo }}</span>
                </div>

                <div class="version-column create-date-col">
                  <span class="create-date">{{ getCreateDate(version) | date:'yyyy/MM/dd HH:mm' }}</span>
                </div>

                <div class="version-column select-col">
                  <div class="version-select-indicator" [class.selected]="selectedVersion() === version">
                    <i class="pi pi-check" *ngIf="selectedVersion() === version"></i>
                    <i class="pi pi-circle" *ngIf="selectedVersion() !== version"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </app-dialog-popup>

        <!-- 簽署確認 Dialog -->
        <app-dialog-popup [(visible)]="showSignatureDialog"
          [textData]="{header: '電子簽名', content: '', titleButtonLeft: '', titleButtonRight: ''}">

          <div class="signature-dialog-content">
            <!-- 載入狀態 -->
            <div *ngIf="isSignatureLoading()" class="dialog-loading">
              <div class="loading-spinner">
                <i class="icon-loader"></i>
                <span>處理簽署中...</span>
              </div>
            </div>

            <!-- 簽名內容 -->
            <div *ngIf="!isSignatureLoading()" class="signature-content">
              <!-- 電子簽名區 -->
              <div class="signature-pad-section">
                <h4 class="signature-title">
                  <i class="pi pi-pencil"></i>
                  請在下方簽名
                </h4>
                <div class="signature-pad-container">
                  <app-signature-pad #signaturePad (signatureData)="handleSignatureData($event)">
                  </app-signature-pad>
                </div>
                <div class="signature-actions">
                  <button class="btn btn-outline" (click)="clearSignature()">
                    <i class="pi pi-refresh"></i>
                    重新簽名
                  </button>
                  <button class="btn btn-primary" (click)="confirmSignature()" [disabled]="!hasValidSignature()">
                    <i class="pi pi-check"></i>
                    確認簽署
                  </button>
                </div>
              </div>
            </div>
          </div>
        </app-dialog-popup>

        <!-- 主要內容區域 -->
        <div class="quotation-content">
          <div class="quotation-details" *ngIf="selectedVersion()">

            <!-- 報價項目 -->
            <div class="section items-section">
              <div class="section-card">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <h2 class="section-title">
                      <i class="icon-list"></i>
                      報價項目
                    </h2>
                    <div class="items-count-badge" *ngIf="selectedVersion()?.tblQuotationItems">
                      <i class="icon-hash"></i>
                      <span class="count-text">{{ selectedVersion()?.tblQuotationItems?.length || 0 }}</span>
                      <span class="count-label">個項目</span>
                    </div>
                  </div>
                </div>
                <div class="items-table">
                  <div class="table-header">
                    <div class="col-item">
                      <i class="icon-package"></i>
                      項目名稱
                    </div>
                    <div class="col-unit">
                      <i class="icon-tag"></i>
                      單位
                    </div>
                    <div class="col-qty">
                      <i class="icon-hash"></i>
                      數量
                    </div>
                    <div class="col-price">
                      <i class="icon-dollar-sign"></i>
                      單價
                    </div>
                    <div class="col-total">
                      <i class="icon-calculator"></i>
                      小計
                    </div>
                    <div class="col-remark">
                      <i class="icon-message-square"></i>
                      備註
                    </div>
                  </div>
                  <div class="table-body">
                    <div class="table-row" *ngFor="let item of selectedVersion()?.tblQuotationItems; let i = index"
                      [class.row-even]="i % 2 === 0">
                      <div class="col-item">
                        <div class="item-info">
                          <span class="item-name">{{ item.CItemName }}</span>
                        </div>
                      </div>
                      <div class="col-unit">
                        <span class="unit-value">{{ item.CUnit || '-' }}</span>
                      </div>
                      <div class="col-qty">
                        <span class="qty-value">{{ item.CCount }}</span>
                      </div>
                      <div class="col-price">
                        <span class="price-value">NT$ {{ item.CUnitPrice | number:'1.0-0' }}</span>
                      </div>
                      <div class="col-total">
                        <span class="total-value">NT$ {{ item.CSubtotal | number:'1.0-0' }}</span>
                      </div>
                      <div class="col-remark">
                        <span class="remark-value">{{ item.CRemark || '-' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 金額計算 -->
          <div class="section calculation-section">
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <i class="icon-calculator"></i>
                  金額明細
                </h2>
              </div>
              <div class="calculation-table">
                <!-- 小計 -->
                <div class="calc-row subtotal-row">
                  <div class="calc-label">
                    <i class="icon-plus"></i>
                    <span>小計</span>
                  </div>
                  <div class="calc-value">
                    NT$ {{ calculateSubtotal(selectedVersion()!.tblQuotationItems) | number:'1.0-0' }}
                  </div>
                </div>

                <!-- 額外費用區域 (自定義) -->
                <div class="calc-row other-fee-row"
                  *ngIf="selectedVersion()!.CShowOther && selectedVersion()!.COtherPercent">
                  <div class="calc-label">
                    <i class="icon-plus-circle"></i>
                    <span>{{ selectedVersion()!.COtherName || '額外費用' }} ({{ selectedVersion()!.COtherPercent }}%)</span>
                  </div>
                  <div class="calc-value">
                    NT$ {{ calculateOtherFee(selectedVersion()!.tblQuotationItems, selectedVersion()!.COtherPercent) |
                    number:'1.0-0' }}
                  </div>
                </div>

                <!-- 總計 -->
                <div class="calc-row total-row">
                  <div class="calc-label">
                    <i class="icon-dollar-sign"></i>
                    <span>總計</span>
                  </div>
                  <div class="calc-value total-amount">
                    NT$ {{ calculateTotalWithOther(selectedVersion()!) | number:'1.0-0' }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 備註 -->
          <div class="section notes-section">
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <i class="icon-info"></i>
                  重要備註
                </h2>
              </div>
              <div class="notes-content">
                <div class="note-category">
                  <h4 class="note-category-title">
                    <i class="icon-clock"></i>
                    效期與時間
                  </h4>
                  <div class="note-item">
                    <span class="note-text">報價單有效期限為30天</span>
                  </div>
                </div>

                <div class="note-category">
                  <h4 class="note-category-title">
                    <i class="icon-dollar-sign"></i>
                    價格說明
                  </h4>
                  <div class="note-item">
                    <span class="note-text">價格含稅</span>
                  </div>
                </div>

                <div class="note-category">
                  <h4 class="note-category-title">
                    <i class="icon-credit-card"></i>
                    付款條件
                  </h4>
                  <div class="note-item">
                    <span class="note-text">付款方式：交貨後30天內付款</span>
                  </div>
                </div>

                <div class="note-category">
                  <h4 class="note-category-title">
                    <i class="icon-help-circle"></i>
                    聯繫方式
                  </h4>
                  <div class="note-item">
                    <span class="note-text">如有疑問請洽詢業務人員</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div> <!-- 關閉主要內容區塊 -->

  </div> <!-- 關閉content -->
</div> <!-- 關閉wrapper -->
