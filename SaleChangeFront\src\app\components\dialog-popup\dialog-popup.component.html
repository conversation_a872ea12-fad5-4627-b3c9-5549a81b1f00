<p-dialog [(visible)]="visible" [modal]="true" [draggable]="false" [closable]="getClosableValue()" (onHide)="onHide()"
    [header]="textData?.header" [styleClass]="getDialogStyleClass()">
    <div class="w-[550px] pb-8  max-sm:w-[380px] max-md:w-[500px]">
        <div *ngIf="textData?.content !== '' else contentZone" class="mt-3 w-full text-center px-6 text-xl font-medium">
            {{textData?.content}}
        </div>
        <ng-template #contentZone>
            <ng-content></ng-content>
        </ng-template>
        <div
            class="mt-6 flex items-center justify-center max-sm:!justify-between w-full max-sm:px-0 text-sm font-light">
            <button *ngIf="textData?.titleButtonLeft !== ''" class="button1  butn1 mr-1"
                (click)="actionButtonLeft.emit()">
                {{textData?.titleButtonLeft}}
            </button>
            <button *ngIf="textData?.titleButtonRight !== ''" class="button2  butn1 ml-1"
                (click)="actionButtonRight.emit()">
                {{textData?.titleButtonRight}}
            </button>
        </div>
    </div>
</p-dialog>
