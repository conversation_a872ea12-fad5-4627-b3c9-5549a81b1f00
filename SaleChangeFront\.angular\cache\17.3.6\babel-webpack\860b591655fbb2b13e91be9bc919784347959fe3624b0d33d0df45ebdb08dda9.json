{"ast": null, "code": "import { signal } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { EnumStatusCode } from '../../../services/api/models/enum-status-code';\nimport { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';\nimport { SignaturePadComponent } from '../../components/signature-pad/signature-pad.component';\nimport { QUOTATION_TEMPLATE } from '../../../assets/template/quotation-template';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = [\"signaturePad\"];\nconst _c1 = () => ({\n  header: \"\\u96FB\\u5B50\\u7C3D\\u540D\",\n  content: \"\",\n  titleButtonLeft: \"\",\n  titleButtonRight: \"\"\n});\nfunction QuotationComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7);\n    i0.ɵɵtext(2, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QuotationComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵelement(2, \"i\", 10);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadQuotationHistory());\n    });\n    i0.ɵɵtext(5, \"\\u91CD\\u65B0\\u8F09\\u5165\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error(), \" \");\n  }\n}\nfunction QuotationComponent_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16);\n    i0.ɵɵelement(3, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 18);\n    i0.ɵɵtext(5, \"\\u66AB\\u7121\\u5831\\u50F9\\u55AE\\u8A18\\u9304\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 19);\n    i0.ɵɵtext(7, \" \\u76EE\\u524D\\u7CFB\\u7D71\\u4E2D\\u6C92\\u6709\\u4EFB\\u4F55\\u5831\\u50F9\\u55AE\\uFF0C\");\n    i0.ɵɵelement(8, \"br\");\n    i0.ɵɵtext(9, \" \\u8ACB\\u78BA\\u8A8D\\u662F\\u5426\\u6709\\u5831\\u50F9\\u55AE\\u8CC7\\u6599\\u6216\\u5617\\u8A66\\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loadQuotationHistory());\n    });\n    i0.ɵɵelement(12, \"i\", 21);\n    i0.ɵɵtext(13, \" \\u91CD\\u65B0\\u8F09\\u5165\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"span\", 68);\n    i0.ɵɵelement(3, \"i\", 69);\n    i0.ɵɵtext(4, \" \\u5831\\u50F9\\u55AE\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 67)(8, \"span\", 68);\n    i0.ɵɵelement(9, \"i\", 71);\n    i0.ɵɵtext(10, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 70)(12, \"span\", 72);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 67)(15, \"span\", 68);\n    i0.ɵɵelement(16, \"i\", 73);\n    i0.ɵɵtext(17, \" \\u5EFA\\u7ACB\\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 70);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 74)(22, \"span\", 68);\n    i0.ɵɵelement(23, \"i\", 75);\n    i0.ɵɵtext(24, \" \\u7E3D\\u91D1\\u984D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 76);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedVersion().CversionNo);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.selectedVersion()));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusText(ctx_r1.selectedVersion()), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 5, ctx_r1.getCreateDate(ctx_r1.selectedVersion()), \"yyyy/MM/dd\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(27, 8, ctx_r1.selectedVersion().CTotalAmount, \"1.0-0\"), \"\");\n  }\n}\nfunction QuotationComponent_div_4_div_4_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.openSignatureDialog());\n    });\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7DDA\\u4E0A\\u7C3D\\u7F72\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QuotationComponent_div_4_div_4_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewContract());\n    });\n    i0.ɵɵelement(1, \"i\", 69);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u67E5\\u770B\\u5408\\u7D04\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 7);\n    i0.ɵɵelement(2, \"i\", 81);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u7248\\u672C\\u8CC7\\u6599\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83);\n    i0.ɵɵelement(2, \"i\", 84);\n    i0.ɵɵelementStart(3, \"span\", 85);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_div_18_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.loadQuotationHistory());\n    });\n    i0.ɵɵelement(6, \"i\", 21);\n    i0.ɵɵtext(7, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error());\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"i\", 17);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u66AB\\u7121\\u7248\\u672C\\u8A18\\u9304\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_20_div_19_i_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 107);\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_20_div_19_i_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 108);\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_20_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_div_20_div_19_Template_div_click_0_listener() {\n      const version_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectVersion(version_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 99)(2, \"span\", 100);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 101)(5, \"span\", 102);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 103)(9, \"div\", 104);\n    i0.ɵɵtemplate(10, QuotationComponent_div_4_div_4_div_20_div_19_i_10_Template, 1, 0, \"i\", 105)(11, QuotationComponent_div_4_div_4_div_20_div_19_i_11_Template, 1, 0, \"i\", 106);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const version_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.selectedVersion() === version_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(version_r9.CversionNo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 8, ctx_r1.getCreateDate(version_r9), \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.selectedVersion() === version_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion() === version_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion() !== version_r9);\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89)(2, \"h4\", 90);\n    i0.ɵɵelement(3, \"i\", 91);\n    i0.ɵɵtext(4, \" \\u7248\\u672C\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 92);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 93)(8, \"div\", 94);\n    i0.ɵɵelement(9, \"i\", 69);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"\\u5831\\u50F9\\u55AE\\u865F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 95);\n    i0.ɵɵelement(13, \"i\", 73);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u5EFA\\u7ACB\\u65E5\\u671F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 96)(17, \"span\");\n    i0.ɵɵtext(18, \"\\u9078\\u64C7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(19, QuotationComponent_div_4_div_4_div_20_div_19_Template, 12, 11, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r1.quotationVersions().length, \" \\u500B\\u7248\\u672C \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.quotationVersions());\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 7);\n    i0.ɵɵelement(2, \"i\", 81);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u8655\\u7406\\u7C3D\\u7F72\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"div\", 110)(2, \"h4\", 111);\n    i0.ɵɵelement(3, \"i\", 78);\n    i0.ɵɵtext(4, \" \\u8ACB\\u5728\\u4E0B\\u65B9\\u7C3D\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 112)(6, \"app-signature-pad\", 113, 0);\n    i0.ɵɵlistener(\"signatureData\", function QuotationComponent_div_4_div_4_div_24_Template_app_signature_pad_signatureData_6_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleSignatureData($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 114)(9, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_div_24_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearSignature());\n    });\n    i0.ɵɵelement(10, \"i\", 116);\n    i0.ɵɵtext(11, \" \\u91CD\\u65B0\\u7C3D\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_div_24_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.confirmSignature());\n    });\n    i0.ɵɵelement(13, \"i\", 107);\n    i0.ɵɵtext(14, \" \\u78BA\\u8A8D\\u7C3D\\u7F72 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.hasValidSignature());\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_26_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137);\n    i0.ɵɵelement(1, \"i\", 130);\n    i0.ɵɵelementStart(2, \"span\", 138);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 139);\n    i0.ɵɵtext(5, \"\\u500B\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(((tmp_4_0 = ctx_r1.selectedVersion()) == null ? null : tmp_4_0.tblQuotationItems == null ? null : tmp_4_0.tblQuotationItems.length) || 0);\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_26_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 140)(1, \"div\", 125)(2, \"div\", 141)(3, \"span\", 142);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 127)(6, \"span\", 143);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 129)(9, \"span\", 144);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 131)(12, \"span\", 145);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 132)(16, \"span\", 146);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 133)(20, \"span\", 147);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    i0.ɵɵclassProp(\"row-even\", i_r12 % 2 === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r11.CItemName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r11.CUnit || \"-\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r11.CCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(14, 8, item_r11.CUnitPrice, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(18, 11, item_r11.CSubtotal, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r11.CRemark || \"-\");\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119)(2, \"div\", 43)(3, \"div\", 44)(4, \"div\", 120)(5, \"h2\", 45);\n    i0.ɵɵelement(6, \"i\", 121);\n    i0.ɵɵtext(7, \" \\u5831\\u50F9\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, QuotationComponent_div_4_div_4_div_26_div_8_Template, 6, 1, \"div\", 122);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 123)(10, \"div\", 124)(11, \"div\", 125);\n    i0.ɵɵelement(12, \"i\", 126);\n    i0.ɵɵtext(13, \" \\u9805\\u76EE\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 127);\n    i0.ɵɵelement(15, \"i\", 128);\n    i0.ɵɵtext(16, \" \\u55AE\\u4F4D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 129);\n    i0.ɵɵelement(18, \"i\", 130);\n    i0.ɵɵtext(19, \" \\u6578\\u91CF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 131);\n    i0.ɵɵelement(21, \"i\", 54);\n    i0.ɵɵtext(22, \" \\u55AE\\u50F9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 132);\n    i0.ɵɵelement(24, \"i\", 46);\n    i0.ɵɵtext(25, \" \\u5C0F\\u8A08 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 133);\n    i0.ɵɵelement(27, \"i\", 134);\n    i0.ɵɵtext(28, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 135);\n    i0.ɵɵtemplate(30, QuotationComponent_div_4_div_4_div_26_div_30_Template, 22, 14, \"div\", 136);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.selectedVersion()) == null ? null : tmp_3_0.tblQuotationItems);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", (tmp_4_0 = ctx_r1.selectedVersion()) == null ? null : tmp_4_0.tblQuotationItems);\n  }\n}\nfunction QuotationComponent_div_4_div_4_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 149);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 51);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.selectedVersion().COtherName || \"\\u984D\\u5916\\u8CBB\\u7528\", \" (\", ctx_r1.selectedVersion().COtherPercent, \"%)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" NT$ \", i0.ɵɵpipeBind2(7, 3, ctx_r1.calculateOtherFee(ctx_r1.selectedVersion().tblQuotationItems, ctx_r1.selectedVersion().COtherPercent), \"1.0-0\"), \" \");\n  }\n}\nfunction QuotationComponent_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵtemplate(3, QuotationComponent_div_4_div_4_div_3_Template, 28, 11, \"div\", 24);\n    i0.ɵɵelementStart(4, \"div\", 25)(5, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleVersionHistory());\n    });\n    i0.ɵɵelement(6, \"i\", 27);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"\\u7248\\u672C\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.printPreview());\n    });\n    i0.ɵɵelement(10, \"i\", 29);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"\\u9810\\u89BD\\u5217\\u5370\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, QuotationComponent_div_4_div_4_button_13_Template, 4, 0, \"button\", 30)(14, QuotationComponent_div_4_div_4_button_14_Template, 4, 0, \"button\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"app-dialog-popup\", 32);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function QuotationComponent_div_4_div_4_Template_app_dialog_popup_visibleChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.showVersionHistory, $event) || (ctx_r1.showVersionHistory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(16, \"div\", 33);\n    i0.ɵɵtemplate(17, QuotationComponent_div_4_div_4_div_17_Template, 5, 0, \"div\", 34)(18, QuotationComponent_div_4_div_4_div_18_Template, 8, 1, \"div\", 35)(19, QuotationComponent_div_4_div_4_div_19_Template, 5, 0, \"div\", 36)(20, QuotationComponent_div_4_div_4_div_20_Template, 20, 2, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"app-dialog-popup\", 32);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function QuotationComponent_div_4_div_4_Template_app_dialog_popup_visibleChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.showSignatureDialog, $event) || (ctx_r1.showSignatureDialog = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(22, \"div\", 38);\n    i0.ɵɵtemplate(23, QuotationComponent_div_4_div_4_div_23_Template, 5, 0, \"div\", 34)(24, QuotationComponent_div_4_div_4_div_24_Template, 15, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 40);\n    i0.ɵɵtemplate(26, QuotationComponent_div_4_div_4_div_26_Template, 31, 2, \"div\", 41);\n    i0.ɵɵelementStart(27, \"div\", 42)(28, \"div\", 43)(29, \"div\", 44)(30, \"h2\", 45);\n    i0.ɵɵelement(31, \"i\", 46);\n    i0.ɵɵtext(32, \" \\u91D1\\u984D\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 47)(34, \"div\", 48)(35, \"div\", 49);\n    i0.ɵɵelement(36, \"i\", 50);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"\\u5C0F\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 51);\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(42, QuotationComponent_div_4_div_4_div_42_Template, 8, 6, \"div\", 52);\n    i0.ɵɵelementStart(43, \"div\", 53)(44, \"div\", 49);\n    i0.ɵɵelement(45, \"i\", 54);\n    i0.ɵɵelementStart(46, \"span\");\n    i0.ɵɵtext(47, \"\\u7E3D\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 55);\n    i0.ɵɵtext(49);\n    i0.ɵɵpipe(50, \"number\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(51, \"div\", 56)(52, \"div\", 43)(53, \"div\", 44)(54, \"h2\", 45);\n    i0.ɵɵelement(55, \"i\", 57);\n    i0.ɵɵtext(56, \" \\u91CD\\u8981\\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 58)(58, \"div\", 59)(59, \"h4\", 60);\n    i0.ɵɵelement(60, \"i\", 61);\n    i0.ɵɵtext(61, \" \\u6548\\u671F\\u8207\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"div\", 62)(63, \"span\", 63);\n    i0.ɵɵtext(64, \"\\u5831\\u50F9\\u55AE\\u6709\\u6548\\u671F\\u9650\\u70BA30\\u5929\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 59)(66, \"h4\", 60);\n    i0.ɵɵelement(67, \"i\", 54);\n    i0.ɵɵtext(68, \" \\u50F9\\u683C\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 62)(70, \"span\", 63);\n    i0.ɵɵtext(71, \"\\u50F9\\u683C\\u542B\\u7A05\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(72, \"div\", 59)(73, \"h4\", 60);\n    i0.ɵɵelement(74, \"i\", 64);\n    i0.ɵɵtext(75, \" \\u4ED8\\u6B3E\\u689D\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"div\", 62)(77, \"span\", 63);\n    i0.ɵɵtext(78, \"\\u4ED8\\u6B3E\\u65B9\\u5F0F\\uFF1A\\u4EA4\\u8CA8\\u5F8C30\\u5929\\u5167\\u4ED8\\u6B3E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(79, \"div\", 59)(80, \"h4\", 60);\n    i0.ɵɵelement(81, \"i\", 65);\n    i0.ɵɵtext(82, \" \\u806F\\u7E6B\\u65B9\\u5F0F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"div\", 62)(84, \"span\", 63);\n    i0.ɵɵtext(85, \"\\u5982\\u6709\\u7591\\u554F\\u8ACB\\u6D3D\\u8A62\\u696D\\u52D9\\u4EBA\\u54E1\");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion());\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion() && ctx_r1.convertStatusFromAPI(ctx_r1.selectedVersion().CQuotationStatus) !== \"confirmed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion() && ctx_r1.convertStatusFromAPI(ctx_r1.selectedVersion().CQuotationStatus) === \"confirmed\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.showVersionHistory);\n    i0.ɵɵproperty(\"textData\", ctx_r1.versionHistoryTextData);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.error());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading() && !ctx_r1.error() && ctx_r1.quotationVersions().length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading() && !ctx_r1.error() && ctx_r1.quotationVersions().length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.showSignatureDialog);\n    i0.ɵɵproperty(\"textData\", i0.ɵɵpureFunction0(23, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSignatureLoading());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSignatureLoading());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion());\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" NT$ \", i0.ɵɵpipeBind2(41, 17, ctx_r1.calculateSubtotal(ctx_r1.selectedVersion().tblQuotationItems), \"1.0-0\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion().CShowOther && ctx_r1.selectedVersion().COtherPercent);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" NT$ \", i0.ɵɵpipeBind2(50, 20, ctx_r1.calculateTotalWithOther(ctx_r1.selectedVersion()), \"1.0-0\"), \" \");\n  }\n}\nfunction QuotationComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 12);\n    i0.ɵɵtext(2, \"\\u5831\\u50F9\\u55AE\\u7BA1\\u7406\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, QuotationComponent_div_4_div_3_Template, 14, 0, \"div\", 13)(4, QuotationComponent_div_4_div_4_Template, 86, 24, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.quotationVersions().length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.quotationVersions().length > 0);\n  }\n}\nexport class QuotationComponent {\n  constructor(quotationService) {\n    this.quotationService = quotationService;\n    this.quotationVersions = signal([]);\n    this.selectedVersion = signal(null);\n    this.showVersionHistory = signal(false);\n    this.isLoading = signal(false);\n    this.error = signal(null);\n    // 簽署相關屬性\n    this.showSignatureDialog = signal(false);\n    this.isSignatureLoading = signal(false);\n    this.hasValidSignature = signal(false);\n    this.signatureData = signal(null);\n    // 版本歷程 Dialog 的 textData\n    this.versionHistoryTextData = {\n      title: 'versionHistory',\n      header: '版本歷程',\n      content: '',\n      titleButtonLeft: '',\n      titleButtonRight: '',\n      showCloseButton: true\n    };\n  }\n  ngOnInit() {\n    this.loadQuotationHistory();\n  }\n  loadQuotationHistory() {\n    this.isLoading.set(true);\n    this.error.set(null);\n    this.quotationService.apiQuotationGetQuotationHistoryPost$Json().subscribe({\n      next: response => {\n        if (response.Entries) {\n          this.quotationVersions.set(response.Entries);\n          if (response.Entries.length > 0) {\n            this.selectedVersion.set(response.Entries[0]); // 選擇第一個版本\n          }\n        } else {\n          this.quotationVersions.set([]); // 確保設置為空陣列\n        }\n        this.isLoading.set(false);\n      },\n      error: error => {\n        console.error('載入報價歷程失敗:', error);\n        this.error.set('載入報價歷程失敗，請稍後再試。');\n        this.quotationVersions.set([]); // 確保錯誤時清空資料\n        this.selectedVersion.set(null);\n        this.isLoading.set(false);\n      }\n    });\n  }\n  selectVersion(version) {\n    this.selectedVersion.set(version);\n    this.showVersionHistory.set(false); // 選擇後關閉 dialog\n  }\n  toggleVersionHistory() {\n    this.showVersionHistory.set(!this.showVersionHistory());\n  }\n  onVersionHistoryClose() {\n    this.showVersionHistory.set(false);\n  }\n  // 輔助方法來獲取狀態樣式類別\n  getStatusClass(version) {\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\n    switch (status) {\n      case 'pending':\n        return 'status-pending';\n      case 'quoted':\n        return 'status-quoted';\n      case 'confirmed':\n        return 'status-confirmed';\n      case 'sent':\n        return 'status-sent';\n      case 'approved':\n        return 'status-approved';\n      case 'rejected':\n        return 'status-rejected';\n      case 'expired':\n        return 'status-expired';\n      default:\n        return 'status-pending';\n    }\n  }\n  // 輔助方法來獲取狀態圖示\n  getStatusIcon(version) {\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\n    switch (status) {\n      case 'pending':\n        return 'icon-clock';\n      case 'quoted':\n        return 'icon-file-text';\n      case 'confirmed':\n        return 'icon-check-circle';\n      case 'sent':\n        return 'icon-send';\n      case 'approved':\n        return 'icon-check-circle-2';\n      case 'rejected':\n        return 'icon-x-circle';\n      case 'expired':\n        return 'icon-alert-triangle';\n      default:\n        return 'icon-clock';\n    }\n  }\n  // 輔助方法來獲取狀態文字\n  getStatusText(version) {\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\n    switch (status) {\n      case 'pending':\n        return '待報價';\n      case 'quoted':\n        return '已報價';\n      case 'confirmed':\n        return '已簽回';\n      case 'sent':\n        return '已發送';\n      case 'approved':\n        return '已核准';\n      case 'rejected':\n        return '已拒絕';\n      case 'expired':\n        return '已過期';\n      default:\n        return '待報價';\n    }\n  }\n  printPreview() {\n    // 創建新視窗顯示套印模板\n    const printWindow = window.open('', '_blank', 'width=800,height=600');\n    if (printWindow && this.selectedVersion()) {\n      const templateContent = this.generatePrintTemplate();\n      printWindow.document.write(templateContent);\n      printWindow.document.close();\n      // 等待內容載入後執行列印\n      printWindow.onload = () => {\n        printWindow.print();\n        printWindow.close();\n      };\n    }\n  }\n  generatePrintTemplate() {\n    const version = this.selectedVersion();\n    if (!version) return '';\n    // 使用新的模板\n    let template = QUOTATION_TEMPLATE;\n    // 準備模板變數\n    const items = version.tblQuotationItems || [];\n    const subtotal = this.calculateSubtotal(items);\n    const otherFee = version.CShowOther ? this.calculateOtherFee(items, version.COtherPercent) : 0;\n    const totalAmount = this.calculateTotalWithOther(version);\n    // 生成項目HTML\n    const itemsHtml = items.map((item, index) => `\n      <tr>\n        <td class=\"text-center\">${index + 1}</td>\n        <td>${item.CItemName || ''}</td>\n        <td class=\"text-right\">NT$ ${(item.CUnitPrice || 0).toLocaleString()}</td>\n        <td class=\"text-center\">${item.CUnit || ''}</td>\n        <td class=\"text-center\">${item.CCount || 0}</td>\n        <td class=\"text-right\">NT$ ${(item.CSubtotal || 0).toLocaleString()}</td>\n        <td>${item.CRemark || ''}</td>\n      </tr>\n    `).join('');\n    // 生成額外費用HTML\n    const additionalFeeHtml = version.CShowOther && version.COtherPercent ? `<div class=\"additional-fee\">${version.COtherName || '額外費用'} (${version.COtherPercent}%)：NT$ ${otherFee.toLocaleString()}</div>` : '';\n    // 替換模板變數\n    template = template.replace(/{{buildCaseName}}/g, '建案名稱') // 這裡可以根據實際需求調整\n    .replace(/{{houseHold}}/g, '戶別資訊') // 這裡可以根據實際需求調整\n    .replace(/{{floor}}/g, '樓層') // 這裡可以根據實際需求調整\n    .replace(/{{printDate}}/g, new Date().toLocaleDateString('zh-TW')).replace(/{{itemsHtml}}/g, itemsHtml).replace(/{{subtotalAmount}}/g, `NT$ ${subtotal.toLocaleString()}`).replace(/{{additionalFeeHtml}}/g, additionalFeeHtml).replace(/{{totalAmount}}/g, `NT$ ${totalAmount.toLocaleString()}`).replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\n    return template;\n  }\n  viewVersionDetails(version) {\n    this.selectedVersion.set(version);\n    // 可以添加額外的檢視邏輯，如滾動到詳情區域\n    const detailsElement = document.querySelector('.quotation-details');\n    if (detailsElement) {\n      detailsElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n    }\n  }\n  compareVersion(version) {\n    // TODO: 實作版本比較功能\n    alert(`比較版本 ${version.CversionNo} 與當前版本 ${this.selectedVersion()?.CversionNo} 的功能開發中...`);\n  }\n  reviseQuotation() {\n    // TODO: 實作修改報價功能\n    alert('修改報價功能開發中...');\n  }\n  viewContract() {\n    // TODO: 實作查看合約功能\n    alert('查看合約功能開發中...');\n  }\n  convertStatusFromAPI(statusCode) {\n    switch (statusCode) {\n      case 1:\n        return 'pending';\n      // 待報價\n      case 2:\n        return 'quoted';\n      // 已報價\n      case 3:\n        return 'confirmed';\n      // 已簽回\n      default:\n        return 'pending';\n      // 預設為待報價\n    }\n  }\n  // 輔助方法來獲取報價編號\n  getQuotationNumber(version) {\n    return `Q${version.CQuotationVersionID}`;\n  }\n  // 輔助方法來獲取版本號\n  getVersionNumber(version) {\n    return version.CversionNo || 'v1.0';\n  }\n  // 輔助方法來獲取建立日期\n  getCreateDate(version) {\n    return version.CCreateDT ? new Date(version.CCreateDT) : new Date();\n  }\n  // 輔助方法來計算小計\n  calculateSubtotal(items) {\n    if (!items) return 0;\n    return items.reduce((sum, item) => sum + (item.CSubtotal || 0), 0);\n  }\n  // 輔助方法來計算稅額\n  calculateTax(items) {\n    const subtotal = this.calculateSubtotal(items);\n    return Math.round(subtotal * 0.1); // 10% 稅率\n  }\n  // 輔助方法來計算額外費用\n  calculateOtherFee(items, otherPercent) {\n    if (!otherPercent || otherPercent <= 0) return 0;\n    const subtotal = this.calculateSubtotal(items);\n    return Math.round(subtotal * (otherPercent / 100));\n  }\n  // 輔助方法來計算含額外費用的總計\n  calculateTotalWithOther(version) {\n    const subtotal = this.calculateSubtotal(version.tblQuotationItems);\n    const otherFee = version.CShowOther ? this.calculateOtherFee(version.tblQuotationItems, version.COtherPercent) : 0;\n    return subtotal + otherFee;\n  }\n  // ===== 簽署相關方法 =====\n  // 開啟簽署對話框\n  openSignatureDialog() {\n    console.log('開啟簽署對話框');\n    console.log('當前版本:', this.selectedVersion());\n    console.log('當前狀態:', this.selectedVersion()?.CQuotationStatus);\n    console.log('轉換後狀態:', this.convertStatusFromAPI(this.selectedVersion()?.CQuotationStatus));\n    // 檢查是否有選中的版本\n    if (!this.selectedVersion()) {\n      alert('請先選擇一個報價版本');\n      return;\n    }\n    // 檢查版本狀態是否允許簽署\n    const currentStatus = this.convertStatusFromAPI(this.selectedVersion()?.CQuotationStatus);\n    if (currentStatus === 'confirmed') {\n      alert('此報價單已經簽署過了');\n      return;\n    }\n    this.showSignatureDialog.set(true);\n    this.hasValidSignature.set(false);\n    this.signatureData.set(null);\n  }\n  // 處理簽名資料\n  handleSignatureData(signatureDataUrl) {\n    console.log('收到簽名資料:', signatureDataUrl?.substring(0, 50) + '...');\n    this.signatureData.set(signatureDataUrl);\n    this.hasValidSignature.set(!!signatureDataUrl && signatureDataUrl.length > 0);\n    // 驗證簽名資料格式 (應該是 data:image/png;base64,... 格式)\n    if (signatureDataUrl && !signatureDataUrl.startsWith('data:image/')) {\n      console.warn('簽名資料格式可能不正確:', signatureDataUrl.substring(0, 100));\n    }\n  }\n  // 清除簽名\n  clearSignature() {\n    if (this.signaturePad) {\n      this.signaturePad.clear();\n      this.hasValidSignature.set(false);\n      this.signatureData.set(null);\n    }\n  }\n  // 確認簽署\n  confirmSignature() {\n    if (!this.hasValidSignature() || !this.selectedVersion()) {\n      return;\n    }\n    this.isSignatureLoading.set(true);\n    // 準備簽署資料\n    const signatureRequest = {\n      CQuotationVersionId: this.selectedVersion().CQuotationVersionID,\n      cSign: this.signatureData()\n    };\n    // 呼叫實際的簽署 API\n    this.quotationService.apiQuotationSignQuotationPost$Json({\n      body: signatureRequest\n    }).subscribe({\n      next: response => {\n        console.log('簽署 API 回應:', response);\n        // 檢查回應狀態碼 (0 通常代表成功)\n        if (response.StatusCode === EnumStatusCode.$0) {\n          this.handleSignatureSuccess();\n        } else {\n          const errorMessage = response.Message || `簽署失敗 (狀態碼: ${response.StatusCode})`;\n          this.handleSignatureError(errorMessage);\n        }\n      },\n      error: error => {\n        console.error('簽署 API 錯誤:', error);\n        this.handleSignatureError(error);\n      }\n    });\n  }\n  // 處理簽署成功\n  handleSignatureSuccess() {\n    this.isSignatureLoading.set(false);\n    this.showSignatureDialog.set(false);\n    // 更新報價單狀態為已簽回\n    const currentVersion = this.selectedVersion();\n    if (currentVersion) {\n      // 更新狀態 (實際情況下這會從 API 回應中取得)\n      currentVersion.CQuotationStatus = 3; // 3 = confirmed (已簽回)\n      this.selectedVersion.set({\n        ...currentVersion\n      });\n    }\n    // 重新載入資料以確保狀態同步\n    this.loadQuotationHistory();\n    // 顯示成功訊息\n    alert('簽署成功！報價單已確認。');\n  }\n  // 處理簽署錯誤\n  handleSignatureError(error) {\n    this.isSignatureLoading.set(false);\n    console.error('簽署失敗:', error);\n    let errorMessage = '簽署失敗，請稍後再試。';\n    // 根據錯誤類型設定不同的錯誤訊息\n    if (typeof error === 'string') {\n      errorMessage = error;\n    } else if (error?.message) {\n      errorMessage = error.message;\n    } else if (error?.error?.message) {\n      errorMessage = error.error.message;\n    }\n    alert(errorMessage);\n  }\n  static #_ = this.ɵfac = function QuotationComponent_Factory(t) {\n    return new (t || QuotationComponent)(i0.ɵɵdirectiveInject(i1.QuotationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: QuotationComponent,\n    selectors: [[\"app-quotation\"]],\n    viewQuery: function QuotationComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.signaturePad = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 5,\n    vars: 3,\n    consts: [[\"signaturePad\", \"\"], [1, \"wrapper\"], [1, \"content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"error-container\"], [1, \"error-message\"], [1, \"icon-alert\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"page-title\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-icon\"], [1, \"icon-file-text\"], [1, \"empty-title\"], [1, \"empty-description\"], [1, \"empty-actions\"], [1, \"icon-refresh-cw\"], [1, \"quotation-header-compact\"], [1, \"quotation-summary-card\"], [\"class\", \"basic-info-row\", 4, \"ngIf\"], [1, \"action-buttons-row\"], [1, \"btn-compact\", \"btn-outline\", 3, \"click\"], [1, \"pi\", \"pi-history\"], [1, \"btn-compact\", \"btn-primary\", 3, \"click\"], [1, \"pi\", \"pi-print\"], [\"class\", \"btn-compact btn-warning\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-compact btn-success\", 3, \"click\", 4, \"ngIf\"], [3, \"visibleChange\", \"visible\", \"textData\"], [1, \"version-dialog-content\"], [\"class\", \"dialog-loading\", 4, \"ngIf\"], [\"class\", \"dialog-error\", 4, \"ngIf\"], [\"class\", \"dialog-empty\", 4, \"ngIf\"], [\"class\", \"version-list-simple\", 4, \"ngIf\"], [1, \"signature-dialog-content\"], [\"class\", \"signature-content\", 4, \"ngIf\"], [1, \"quotation-content\"], [\"class\", \"quotation-details\", 4, \"ngIf\"], [1, \"section\", \"calculation-section\"], [1, \"section-card\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"icon-calculator\"], [1, \"calculation-table\"], [1, \"calc-row\", \"subtotal-row\"], [1, \"calc-label\"], [1, \"icon-plus\"], [1, \"calc-value\"], [\"class\", \"calc-row other-fee-row\", 4, \"ngIf\"], [1, \"calc-row\", \"total-row\"], [1, \"icon-dollar-sign\"], [1, \"calc-value\", \"total-amount\"], [1, \"section\", \"notes-section\"], [1, \"icon-info\"], [1, \"notes-content\"], [1, \"note-category\"], [1, \"note-category-title\"], [1, \"icon-clock\"], [1, \"note-item\"], [1, \"note-text\"], [1, \"icon-credit-card\"], [1, \"icon-help-circle\"], [1, \"basic-info-row\"], [1, \"info-group\"], [1, \"info-label\"], [1, \"pi\", \"pi-file\"], [1, \"info-value\"], [1, \"pi\", \"pi-info-circle\"], [1, \"compact\", 3, \"ngClass\"], [1, \"pi\", \"pi-calendar\"], [1, \"info-group\", \"amount-group\"], [1, \"pi\", \"pi-dollar\"], [1, \"info-value\", \"amount\"], [1, \"btn-compact\", \"btn-warning\", 3, \"click\"], [1, \"pi\", \"pi-pencil\"], [1, \"btn-compact\", \"btn-success\", 3, \"click\"], [1, \"dialog-loading\"], [1, \"icon-loader\"], [1, \"dialog-error\"], [1, \"error-content\"], [1, \"icon-alert-triangle\"], [1, \"error-text\"], [1, \"dialog-empty\"], [1, \"empty-content\"], [1, \"version-list-simple\"], [1, \"version-list-header\"], [1, \"version-list-title\"], [1, \"pi\", \"pi-list\"], [1, \"version-count\"], [1, \"version-table-header\"], [1, \"header-column\", \"quotation-number-col\"], [1, \"header-column\", \"create-date-col\"], [1, \"header-column\", \"select-col\"], [\"class\", \"version-item-simple\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"version-item-simple\", 3, \"click\"], [1, \"version-column\", \"quotation-number-col\"], [1, \"quotation-number\"], [1, \"version-column\", \"create-date-col\"], [1, \"create-date\"], [1, \"version-column\", \"select-col\"], [1, \"version-select-indicator\"], [\"class\", \"pi pi-check\", 4, \"ngIf\"], [\"class\", \"pi pi-circle\", 4, \"ngIf\"], [1, \"pi\", \"pi-check\"], [1, \"pi\", \"pi-circle\"], [1, \"signature-content\"], [1, \"signature-pad-section\"], [1, \"signature-title\"], [1, \"signature-pad-container\"], [3, \"signatureData\"], [1, \"signature-actions\"], [1, \"btn\", \"btn-outline\", 3, \"click\"], [1, \"pi\", \"pi-refresh\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"quotation-details\"], [1, \"section\", \"items-section\"], [1, \"section-title-wrapper\"], [1, \"icon-list\"], [\"class\", \"items-count-badge\", 4, \"ngIf\"], [1, \"items-table\"], [1, \"table-header\"], [1, \"col-item\"], [1, \"icon-package\"], [1, \"col-unit\"], [1, \"icon-tag\"], [1, \"col-qty\"], [1, \"icon-hash\"], [1, \"col-price\"], [1, \"col-total\"], [1, \"col-remark\"], [1, \"icon-message-square\"], [1, \"table-body\"], [\"class\", \"table-row\", 3, \"row-even\", 4, \"ngFor\", \"ngForOf\"], [1, \"items-count-badge\"], [1, \"count-text\"], [1, \"count-label\"], [1, \"table-row\"], [1, \"item-info\"], [1, \"item-name\"], [1, \"unit-value\"], [1, \"qty-value\"], [1, \"price-value\"], [1, \"total-value\"], [1, \"remark-value\"], [1, \"calc-row\", \"other-fee-row\"], [1, \"icon-plus-circle\"]],\n    template: function QuotationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵtemplate(2, QuotationComponent_div_2_Template, 3, 0, \"div\", 3)(3, QuotationComponent_div_3_Template, 6, 1, \"div\", 4)(4, QuotationComponent_div_4_Template, 5, 2, \"div\", 5);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.error());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading() && !ctx.error());\n      }\n    },\n    dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe, FormsModule, DialogPopupComponent, SignaturePadComponent],\n    styles: [\"@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}[_nghost-%COMP%]{display:block}*[_ngcontent-%COMP%]{scrollbar-width:thin;scrollbar-color:rgba(184,134,11,.6) rgba(249,250,251,.3)}.wrapper[_ngcontent-%COMP%]{min-height:100vh;background:#f8f7f4}.content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:16px}.page-title[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:700;color:#231815;text-align:center;margin-bottom:1.25rem}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:200px;background:#fff;border-radius:12px;margin:20px 0;box-shadow:0 1px 3px #ae9b661a}.loading-spinner[_ngcontent-%COMP%]{font-size:18px;color:#b8a676;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px;background:#fff;border-radius:12px;margin:20px 0;border:1px solid #FFDD64;box-shadow:0 1px 3px #ae9b661a}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:10px;margin-bottom:20px;font-size:16px;color:#f1502f}.status-draft[_ngcontent-%COMP%]{background:#b8a67626;color:#9b8a5a;border:1px solid rgba(184,166,118,.25)}.status-sent[_ngcontent-%COMP%]{background:#b8a6761a;color:#9b8a5a;border:1px solid rgba(184,166,118,.25)}.status-approved[_ngcontent-%COMP%]{background:#23a4151a;color:#23a415;border:1px solid rgba(35,164,21,.2)}.status-rejected[_ngcontent-%COMP%]{background:#f1502f14;color:#f1502f;border:1px solid rgba(241,80,47,.2)}.status-expired[_ngcontent-%COMP%]{background:#ffdd641a;color:#e4b200;border:1px solid rgba(255,221,100,.2)}.header-actions[_ngcontent-%COMP%]{display:flex;gap:1rem}.btn[_ngcontent-%COMP%]{padding:.5rem 1rem;border:none;border-radius:.5rem;font-weight:500;cursor:pointer;display:flex;align-items:center;gap:.5rem;font-size:.875rem}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 2px 8px #ae9b6633}.btn-secondary[_ngcontent-%COMP%]{background:#b8a6760d;color:#231815;border:1px solid rgba(184,166,118,.2)}.btn-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a415,#23a415e6);color:#fff;box-shadow:#23a41540 0 2px 8px}.quotation-content[_ngcontent-%COMP%]{width:100%;max-height:calc(100vh - 200px);overflow-y:auto;overflow-x:hidden}.quotation-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.quotation-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#fff3;border-radius:3px}.quotation-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b8a67680;border-radius:3px;-webkit-transition:background .3s ease;transition:background .3s ease}.quotation-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#b8a676b3}.quotation-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.quotation-header-simple[_ngcontent-%COMP%]{margin-bottom:1.5rem}.quotation-title-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#fffffff2);border-radius:12px;box-shadow:0 4px 20px #0000000f;border:1px solid rgba(184,166,118,.1);padding:1.5rem;position:relative;overflow:hidden}.quotation-title-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;right:0;width:120px;height:100%;background:linear-gradient(45deg,transparent 0%,rgba(184,166,118,.02) 100%);pointer-events:none}.quotation-title-card[_ngcontent-%COMP%]   .quotation-title[_ngcontent-%COMP%]{margin-bottom:1.25rem;position:relative;z-index:1}.quotation-title-card[_ngcontent-%COMP%]   .quotation-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#231815;margin:0;text-align:center}@media (max-width: 768px){.quotation-title-card[_ngcontent-%COMP%]   .quotation-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.25rem}}.quotation-title-card[_ngcontent-%COMP%]   .basic-info-simple[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:2rem;position:relative;z-index:1}@media (max-width: 768px){.quotation-title-card[_ngcontent-%COMP%]   .basic-info-simple[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;align-items:center}}.quotation-title-card[_ngcontent-%COMP%]   .basic-info-simple[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem;padding:1rem;background:#e6f0f34d;border-radius:10px;border:1px solid rgba(184,166,118,.08);min-width:180px}@media (max-width: 768px){.quotation-title-card[_ngcontent-%COMP%]   .basic-info-simple[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{min-width:200px;width:100%;max-width:280px}}.quotation-title-card[_ngcontent-%COMP%]   .basic-info-simple[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;color:#23181599;font-weight:500}.quotation-title-card[_ngcontent-%COMP%]   .basic-info-simple[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676;font-size:.75rem}.quotation-title-card[_ngcontent-%COMP%]   .basic-info-simple[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{font-size:1rem;color:#231815;font-weight:600;text-align:center}@media (max-width: 768px){.quotation-title-card[_ngcontent-%COMP%]   .basic-info-simple[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{font-size:.95rem}}.quotation-header-compact[_ngcontent-%COMP%]{margin-bottom:1.5rem}.quotation-summary-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#fffffff2);border-radius:12px;box-shadow:0 4px 20px #0000000f;border:1px solid rgba(184,166,118,.1);padding:1.25rem;position:relative;overflow:hidden}.quotation-summary-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;right:0;width:120px;height:100%;background:linear-gradient(45deg,transparent 0%,rgba(184,166,118,.02) 100%);pointer-events:none}.basic-info-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem;margin-bottom:1.25rem;position:relative;z-index:1}@media (max-width: 768px){.basic-info-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:.75rem;margin-bottom:1rem}}@media (max-width: 480px){.basic-info-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:.5rem}}.info-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.375rem;padding:.75rem;background:#e6f0f340;border-radius:8px;border:1px solid rgba(184,166,118,.06)}.info-group.amount-group[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a6760a,#ae9b660f);border-color:#b8a67626}.info-group.amount-group[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{color:#9b8a5a;font-weight:700}@media (max-width: 480px){.info-group[_ngcontent-%COMP%]{padding:.625rem .75rem}}.info-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.375rem;color:#23181599;font-size:.8rem;font-weight:500;text-transform:uppercase;letter-spacing:.3px}.info-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;opacity:.7}.info-value[_ngcontent-%COMP%]{color:#231815;font-size:.9rem;font-weight:600;line-height:1.3}.info-value.amount[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:700}.info-value[_ngcontent-%COMP%]   .status-badge.compact[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.25rem;padding:.25rem .625rem;border-radius:6px;font-size:.75rem;font-weight:500;line-height:1.2;margin:0;min-height:auto}.action-buttons-row[_ngcontent-%COMP%]{display:flex;gap:.75rem;flex-wrap:wrap;position:relative;z-index:1}@media (max-width: 768px){.action-buttons-row[_ngcontent-%COMP%]{gap:.5rem}}@media (max-width: 480px){.action-buttons-row[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}}.btn-compact[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.625rem 1rem;border:none;border-radius:8px;font-weight:500;cursor:pointer;font-size:.875rem;line-height:1.2;min-height:40px}.btn-compact[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}.btn-compact[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{white-space:nowrap}.btn-compact.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 2px 8px #b8a6764d}.btn-compact.btn-outline[_ngcontent-%COMP%]{background:#fff;color:#231815;border:1px solid rgba(184,166,118,.3)}.btn-compact.btn-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffdd64,#f4d03f);color:#231815;border:1px solid rgba(255,221,100,.3);box-shadow:0 2px 8px #ffdd644d;transition:all .3s ease}.btn-compact.btn-warning[_ngcontent-%COMP%]:hover{background:#ffdd640d;border-color:#ffdd6466;color:#fdc600;transform:translateY(-1px);box-shadow:0 4px 12px #ffdd6466}.btn-compact.btn-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a415,#19770f);color:#fff;box-shadow:0 2px 8px #23a4154d}@media (max-width: 768px){.btn-compact[_ngcontent-%COMP%]{padding:.5rem .875rem;font-size:.8rem;min-height:36px}.btn-compact[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}}@media (max-width: 480px){.btn-compact[_ngcontent-%COMP%]{justify-content:center;width:100%}}.quotation-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#fffffff2);border-radius:16px;box-shadow:0 8px 32px #00000014;border:1px solid rgba(184,166,118,.1);margin-bottom:2rem;overflow:hidden;position:relative}.quotation-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;right:0;width:200px;height:100%;background:linear-gradient(45deg,transparent 0%,rgba(184,166,118,.03) 100%);pointer-events:none}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:2rem;gap:2rem;position:relative;z-index:1}@media (max-width: 768px){.header-content[_ngcontent-%COMP%]{flex-direction:column;gap:1.5rem}}.quotation-info[_ngcontent-%COMP%]{flex:1;min-width:0}.quotation-main-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.field-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;font-weight:600;color:#23181599;text-transform:uppercase;letter-spacing:.5px;margin-bottom:.25rem}.field-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem;color:#b8a676;opacity:.8}.field-title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{line-height:1.2}.status-badge[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.25rem;border-radius:12px;font-size:.875rem;font-weight:600;text-transform:uppercase;letter-spacing:.5px;box-shadow:0 3px 12px #0000001a;transition:all .3s ease;min-height:48px;justify-content:center}.status-badge[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 16px #00000026}.status-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}@media (max-width: 768px){.status-badge[_ngcontent-%COMP%]{padding:.625rem 1rem;font-size:.8rem;min-height:44px}.status-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}}.status-badge.status-pending[_ngcontent-%COMP%], .status-badge.status-draft[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67626,#b8a67614);color:#9b8a5a;border:1px solid rgba(184,166,118,.25)}.status-badge.status-sent[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a6761f,#b8a6760f);color:#9b8a5a;border:1px solid rgba(184,166,118,.25)}.status-badge.status-approved[_ngcontent-%COMP%], .status-badge.status-confirmed[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a41526,#23a41514);color:#23a415;border:1px solid rgba(35,164,21,.25)}.status-badge.status-rejected[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f1502f26,#f1502f14);color:#f1502f;border:1px solid rgba(241,80,47,.25)}.status-badge.status-expired[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffdd6426,#ffdd6414);color:#e4b200;border:1px solid rgba(255,221,100,.25)}.quotation-details-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.25rem}@media (max-width: 480px){.quotation-details-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.detail-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem;padding:1rem;background:#e6f0f34d;border-radius:12px;border:1px solid rgba(184,166,118,.08);transition:all .3s ease}.detail-item[_ngcontent-%COMP%]:hover{background:#e6f0f380;border-color:#b8a67626;transform:translateY(-1px)}.detail-item.amount-highlight[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a6760d,#ae9b6614);border-color:#b8a67633}.detail-item.amount-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a67614,#ae9b661f);border-color:#b8a6764d}.detail-item.amount-highlight[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]{color:#9b8a5a;font-weight:700}.detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.25rem;padding:.375rem .75rem;border-radius:6px;font-size:.875rem;font-weight:500;line-height:1.2;margin:0}.detail-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#23181599;font-size:.875rem;font-weight:500;text-transform:uppercase;letter-spacing:.5px}.detail-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem;opacity:.7}.detail-value[_ngcontent-%COMP%]{color:#231815;font-size:1rem;font-weight:600}.detail-value.amount-value[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:700}.header-actions-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67608,#b8a67603);border:2px solid rgba(184,166,118,.15);border-radius:16px;padding:1.5rem;box-shadow:0 4px 20px #b8a67614;position:relative;overflow:hidden;min-width:320px}.header-actions-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:-50%;right:-50%;width:100px;height:100px;background:radial-gradient(circle,rgba(184,166,118,.1) 0%,transparent 70%);border-radius:50%;pointer-events:none}@media (max-width: 768px){.header-actions-container[_ngcontent-%COMP%]{min-width:auto;width:100%;padding:1.25rem}}.actions-header[_ngcontent-%COMP%]{margin-bottom:1.25rem;position:relative;z-index:1}.actions-header[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:1rem;font-weight:600;color:#9b8a5a;margin:0;text-transform:uppercase;letter-spacing:.5px}.actions-header[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.125rem;color:#b8a676}.header-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem;position:relative;z-index:1}@media (max-width: 768px){.header-actions[_ngcontent-%COMP%]{gap:.625rem}}.btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.25rem;border:none;border-radius:12px;font-weight:600;font-size:.875rem;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);text-decoration:none;position:relative;overflow:hidden;min-width:120px;justify-content:center}.btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,rgba(255,255,255,.1) 0%,transparent 100%);opacity:0;transition:opacity .3s ease}.btn[_ngcontent-%COMP%]:hover:before{opacity:1}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{white-space:nowrap}.btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #00000026}.btn[_ngcontent-%COMP%]:active{transform:translateY(0);box-shadow:0 4px 15px #0000001a}.btn-action[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1rem 1.5rem;border-radius:14px;font-weight:600;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);text-decoration:none;position:relative;overflow:hidden;min-width:200px;justify-content:flex-start;border:2px solid transparent}.btn-action[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,rgba(255,255,255,.1) 0%,transparent 100%);opacity:0;transition:opacity .3s ease}.btn-action[_ngcontent-%COMP%]:hover:before{opacity:1}.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:10px;background:#ffffff26;flex-shrink:0;transition:all .3s ease}.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;gap:.25rem;flex:1}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;line-height:1.2}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{font-size:.75rem;opacity:.8;font-weight:400;line-height:1.2}.btn-action[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 12px 30px #00000026}.btn-action[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:#ffffff40;transform:scale(1.05)}.btn-action[_ngcontent-%COMP%]:active{transform:translateY(-1px);box-shadow:0 6px 20px #0000001a}@media (max-width: 768px){.btn-action[_ngcontent-%COMP%]{min-width:auto;padding:.875rem 1.25rem;gap:.875rem}.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{width:36px;height:36px}.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.125rem}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-size:.85rem}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{font-size:.7rem}}.btn-outline[_ngcontent-%COMP%]{background:#fff;color:#231815;border:2px solid rgba(184,166,118,.2);box-shadow:0 2px 8px #0000000d}.btn-outline[_ngcontent-%COMP%]:hover{background:#b8a6760d;border-color:#b8a67666;color:#9b8a5a}.btn-outline.btn-action[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#fffffff2);border:2px solid rgba(184,166,118,.25);color:#231815;box-shadow:0 4px 16px #b8a6761a}.btn-outline.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67626,#b8a67614);border:1px solid rgba(184,166,118,.2)}.btn-outline.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#9b8a5a}.btn-outline.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{color:#231815}.btn-outline.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{color:#23181599}.btn-outline.btn-action[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a67614,#b8a6760d);border-color:#b8a67666;box-shadow:0 8px 25px #b8a67633}.btn-outline.btn-action[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67640,#b8a67626);border-color:#b8a6764d}.btn-outline.btn-action[_ngcontent-%COMP%]:hover   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{color:#9b8a5a}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 4px 16px #b8a6764d}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ae9b66,#9b8a5a);box-shadow:0 6px 20px #b8a67666}.btn-primary.btn-action[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 6px 20px #b8a6764d;border:2px solid rgba(174,155,102,.3)}.btn-primary.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.15)}.btn-primary.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn-primary.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%], .btn-primary.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{color:#fff}.btn-primary.btn-action[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ae9b66,#9b8a5a);box-shadow:0 10px 30px #b8a67666;border-color:#9b8a5a66}.btn-primary.btn-action[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:#ffffff4d;border-color:#ffffff40}.btn-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a415,#23a415e6);color:#fff;box-shadow:0 4px 16px #23a41540}.btn-success[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#23a415f2,#23a415d9);box-shadow:0 6px 20px #23a41559}.btn-success.btn-action[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a415,#23a415e6);color:#fff;box-shadow:0 6px 20px #23a41540;border:2px solid rgba(35,164,21,.3)}.btn-success.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.15)}.btn-success.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn-success.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%], .btn-success.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{color:#fff}.btn-success.btn-action[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#23a415f2,#23a415d9);box-shadow:0 10px 30px #23a41559;border-color:#23a41566}.btn-success.btn-action[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:#ffffff4d;border-color:#ffffff40}.section[_ngcontent-%COMP%]{margin-bottom:1rem}.section-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 1px 3px #ae9b661a;overflow:hidden}.section-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:#231815;margin:0;padding:1.25rem 1.25rem 0}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1.25rem 1.25rem 0}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{padding:0;display:flex;align-items:center;gap:.5rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ae9b66;font-size:1rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;background:linear-gradient(135deg,#b8a6761a,#b8a6760d);border:1px solid rgba(184,166,118,.2);padding:.5rem .75rem;border-radius:20px;font-size:.875rem;color:#9b8a5a;font-weight:500;transition:all .2s ease}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem;color:#ae9b66}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{font-weight:600;color:#9b8a5a;min-width:1.2rem;text-align:center}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-label[_ngcontent-%COMP%]{color:#23181599;margin-left:.1rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a67626,#b8a67614);border-color:#b8a6764d;transform:translateY(-1px);box-shadow:0 2px 8px #b8a67626}@media (max-width: 768px){.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.75rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]{align-self:flex-end}}.items-table[_ngcontent-%COMP%]{margin:1rem;border-radius:8px;overflow:hidden;border:1px solid #E5E3E1;overflow-x:auto}.items-table[_ngcontent-%COMP%]::-webkit-scrollbar{height:6px}.items-table[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#fff3;border-radius:3px}.items-table[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b8a67680;border-radius:3px;-webkit-transition:background .3s ease;transition:background .3s ease}.items-table[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#b8a676b3}.table-header[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1fr 1.5fr 1.5fr 1.5fr;background:#e6f0f3;font-weight:600;color:#231815}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding:1rem;border-right:1px solid #E5E3E1}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-right:none}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1fr 1.5fr 1.5fr 1.5fr;border-bottom:1px solid #E5E3E1;transition:background-color .2s ease}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]:hover{background:#f8f7f4}.table-body[_ngcontent-%COMP%]   .table-row.row-even[_ngcontent-%COMP%]{background:#f8f7f480}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]:last-child{border-bottom:none}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding:1rem;border-right:1px solid #E5E3E1;display:flex;align-items:center}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-right:none}.calculation-table[_ngcontent-%COMP%]{margin:1rem;border-radius:8px;overflow:hidden;border:1px solid #E5E3E1}.calc-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:.875rem 1.25rem;border-bottom:1px solid #E5E3E1}.calc-row[_ngcontent-%COMP%]:last-child{border-bottom:none}.calc-row.total-row[_ngcontent-%COMP%]{background:#e6f0f3;font-weight:600;font-size:1.125rem}.calc-row.other-fee-row[_ngcontent-%COMP%]{background:#ffdd641a;border-left:3px solid #FFDD64}.calc-row.other-fee-row[_ngcontent-%COMP%]   .calc-label[_ngcontent-%COMP%]{color:#9b8a5a;font-weight:500}.calc-row.other-fee-row[_ngcontent-%COMP%]   .calc-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffdd64}.calc-row.other-fee-row[_ngcontent-%COMP%]   .calc-value[_ngcontent-%COMP%]{color:#9b8a5a;font-weight:600}.calc-row[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#231815;font-weight:500}.calc-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#231815;font-weight:600}.total-amount[_ngcontent-%COMP%]{color:#ae9b66;font-size:1.25rem}.notes-content[_ngcontent-%COMP%]{padding:1rem}.notes-content[_ngcontent-%COMP%]   .note-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:1rem;padding:.75rem;background:#f8f7f4;border-radius:6px}.notes-content[_ngcontent-%COMP%]   .note-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.notes-content[_ngcontent-%COMP%]   .note-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676;font-size:1.125rem}.notes-content[_ngcontent-%COMP%]   .note-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#231815;font-weight:500}.dialog-loading[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:200px;padding:40px}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{font-size:16px;color:#b8a676;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}.dialog-error[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:300px;padding:3rem;background:linear-gradient(135deg,#f1502f0d,#fff)}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1.5rem;text-align:center}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:#f1502f}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .error-text[_ngcontent-%COMP%]{font-size:1rem;color:#f1502f;max-width:300px}.dialog-empty[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:300px;padding:3rem;background:linear-gradient(135deg,#fff,#f8f7f44d)}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1rem;text-align:center}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:#23181599;opacity:.6}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:1.125rem;color:#23181599;font-weight:500}  .p-dialog .p-dialog-content{padding:0!important;overflow:hidden}.version-dialog-content[_ngcontent-%COMP%]{width:700px;max-height:70vh;overflow:hidden}@media (max-width: 768px){.version-dialog-content[_ngcontent-%COMP%]{width:95vw;max-height:80vh}}.version-summary[_ngcontent-%COMP%]{padding:20px 24px 16px;background:linear-gradient(135deg,#b8a676,#b8a676cc);border-bottom:1px solid rgba(174,155,102,.2);color:#fff}.version-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px}.version-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]{font-size:14px;color:#ffffffe6;background:#ffffff26;padding:4px 12px;border-radius:20px;border:1px solid rgba(255,255,255,.2)}.version-summary[_ngcontent-%COMP%]   .current-version-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.version-summary[_ngcontent-%COMP%]   .current-version-info[_ngcontent-%COMP%]   .current-label[_ngcontent-%COMP%]{font-size:14px;color:#ffffffe6}.version-summary[_ngcontent-%COMP%]   .current-version-info[_ngcontent-%COMP%]   .current-version[_ngcontent-%COMP%]{font-weight:600;color:#fff;background:#fff3;padding:2px 8px;border-radius:4px;font-size:14px}.version-list-container[_ngcontent-%COMP%]{max-height:50vh;overflow-y:auto;padding:8px 0}.version-list-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.version-list-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f8f7f4;border-radius:3px}.version-list-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b8a676;border-radius:3px}.version-list-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#ae9b66}.version-items[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px 24px;border-bottom:1px solid #E5E3E1;cursor:pointer;transition:all .2s ease;position:relative}.version-items[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a67614,#b8a6760d)}.version-items[_ngcontent-%COMP%]   .version-item.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67626,#b8a6761a);border-left:4px solid #AE9B66;padding-left:20px}.version-items[_ngcontent-%COMP%]   .version-item.active[_ngcontent-%COMP%]   .version-arrow[_ngcontent-%COMP%]{display:block}.version-items[_ngcontent-%COMP%]   .version-item.current[_ngcontent-%COMP%]   .version-current-badge[_ngcontent-%COMP%]{display:inline-flex}.version-items[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]{flex:1}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:12px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]   .version-name[_ngcontent-%COMP%]{font-weight:600;color:#231815;font-size:16px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]   .version-current-badge[_ngcontent-%COMP%]{display:none;align-items:center;gap:4px;background:linear-gradient(135deg,#ae9b66,#9b8a5a);color:#fff;padding:2px 8px;border-radius:12px;font-size:12px;font-weight:500;box-shadow:0 2px 4px #ae9b664d}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]   .version-current-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-pending[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffdd6426,#ffdd641a);color:#ffcc18;border:1px solid rgba(255,221,100,.2)}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-quoted[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67626,#b8a6761a);color:#9b8a5a}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-confirmed[_ngcontent-%COMP%]{background:#23a4151a;color:#23a415}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-sent[_ngcontent-%COMP%]{background:#b8a6761a;color:#ae9b66}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-approved[_ngcontent-%COMP%]{background:#23a4151a;color:#23a415}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-rejected[_ngcontent-%COMP%]{background:#f1502f1a;color:#f1502f}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-expired[_ngcontent-%COMP%]{background:#2318151a;color:#23181599}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-meta[_ngcontent-%COMP%]{margin-bottom:12px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-meta[_ngcontent-%COMP%]   .version-date[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:14px;color:#23181599}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-meta[_ngcontent-%COMP%]   .version-date[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]{display:flex;flex-direction:column}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]   .amount-label[_ngcontent-%COMP%]{font-size:12px;color:#23181599;margin-bottom:2px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]   .amount-value[_ngcontent-%COMP%]{font-weight:600;color:#231815;font-size:14px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-items-count[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-size:12px;color:#23181599;background:#f8f7f4;padding:4px 8px;border-radius:4px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-items-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.version-items[_ngcontent-%COMP%]   .version-arrow[_ngcontent-%COMP%]{display:none;margin-left:12px;color:#ae9b66}.version-items[_ngcontent-%COMP%]   .version-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px;animation:_ngcontent-%COMP%_bounce 1s ease-in-out infinite}.version-list[_ngcontent-%COMP%]{max-height:50vh;overflow-y:auto;padding:0;margin:0}.version-list[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]{padding:12px 16px;border-bottom:1px solid #E5E3E1;cursor:pointer;transition:all .3s ease;background:#fff;position:relative;box-sizing:border-box}.version-list[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]:hover{background:#b8a6760d;border-left:3px solid #B8A676;padding-left:13px;box-shadow:0 1px 3px #ae9b661a}.version-list[_ngcontent-%COMP%]   .version-item.selected[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67614,#b8a67608);border-left:3px solid #AE9B66;padding-left:13px;box-shadow:0 2px 8px #ae9b6633}.version-list[_ngcontent-%COMP%]   .version-item.selected[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-number[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#9b8a5a;font-weight:600}.version-list[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:6px;gap:12px}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-number[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-weight:500;color:#231815;flex:1;overflow:hidden}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-number[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676;font-size:14px;flex-shrink:0}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-number[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:1.2}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]{flex-shrink:0}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:4px;font-size:11px;padding:3px 8px;border-radius:10px;white-space:nowrap}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%]{background:#ffdd641a;color:#fdc600}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-quoted[_ngcontent-%COMP%]{background:#b8a6761a;color:#9b8a5a}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-confirmed[_ngcontent-%COMP%]{background:#23a4151a;color:#19770f}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-sent[_ngcontent-%COMP%]{background:#b8a67626;color:#9b8a5a}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-approved[_ngcontent-%COMP%]{background:#23a4151a;color:#23a415}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-rejected[_ngcontent-%COMP%]{background:#f1502f14;color:#de320f}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%]{background:#ffdd641a;color:#ca9e00}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:4px;color:#23181599;font-size:12px;gap:12px}.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-date[_ngcontent-%COMP%], .version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;white-space:nowrap}.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-date[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;flex-shrink:0;color:#b8a676}.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]{font-weight:500;color:#231815}@media (max-width: 768px){.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:2px}}.version-list[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .items-count[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;color:#23181599;font-size:11px}.version-list[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .items-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px;flex-shrink:0}.version-list[_ngcontent-%COMP%]   .version-other-fee[_ngcontent-%COMP%]   .other-fee-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;color:#9b8a5a;font-size:11px;font-weight:500;background:#ffdd641a;padding:2px 6px;border-radius:4px;margin-top:4px}.version-list[_ngcontent-%COMP%]   .version-other-fee[_ngcontent-%COMP%]   .other-fee-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px;color:#ffdd64;flex-shrink:0}.version-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.version-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f8f7f4;border-radius:3px}.version-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b8a676;border-radius:3px}.version-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#ae9b66}.version-list-simple[_ngcontent-%COMP%]{max-height:50vh;overflow-y:auto;padding:0;margin:0}.version-list-simple[_ngcontent-%COMP%]   .version-list-header[_ngcontent-%COMP%]{padding:16px 20px 12px;border-bottom:2px solid #E5E3E1;background:linear-gradient(135deg,#b8a67608,#b8a67603);display:flex;justify-content:space-between;align-items:center;position:sticky;top:0;z-index:10}.version-list-simple[_ngcontent-%COMP%]   .version-list-header[_ngcontent-%COMP%]   .version-list-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#231815;margin:0}.version-list-simple[_ngcontent-%COMP%]   .version-list-header[_ngcontent-%COMP%]   .version-list-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676;font-size:14px}.version-list-simple[_ngcontent-%COMP%]   .version-list-header[_ngcontent-%COMP%]   .version-count[_ngcontent-%COMP%]{font-size:12px;color:#23181599;background:#b8a6761a;padding:4px 8px;border-radius:12px;border:1px solid rgba(184,166,118,.2)}.version-list-simple[_ngcontent-%COMP%]   .version-table-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 20px;background:#b8a6760d;border-bottom:1px solid #CDCDCD;font-weight:600;font-size:14px;color:#231815;position:sticky;top:60px;z-index:9}.version-list-simple[_ngcontent-%COMP%]   .version-table-header[_ngcontent-%COMP%]   .header-column[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px}.version-list-simple[_ngcontent-%COMP%]   .version-table-header[_ngcontent-%COMP%]   .header-column[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676;font-size:12px}.version-list-simple[_ngcontent-%COMP%]   .version-table-header[_ngcontent-%COMP%]   .header-column.quotation-number-col[_ngcontent-%COMP%]{flex:1.2;min-width:140px}.version-list-simple[_ngcontent-%COMP%]   .version-table-header[_ngcontent-%COMP%]   .header-column.create-date-col[_ngcontent-%COMP%]{flex:1.5;min-width:160px}.version-list-simple[_ngcontent-%COMP%]   .version-table-header[_ngcontent-%COMP%]   .header-column.select-col[_ngcontent-%COMP%]{flex:.6;min-width:60px;justify-content:center}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]{display:flex;align-items:center;padding:14px 20px;border-bottom:1px solid #E5E3E1;cursor:pointer;transition:all .3s ease;background:#fff;position:relative}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]:hover{background:#b8a6760d;border-left:3px solid #B8A676;padding-left:17px;box-shadow:0 1px 3px #ae9b661a}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple.selected[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a6761a,#b8a6760d);border-left:4px solid #AE9B66;padding-left:16px;box-shadow:0 2px 8px #ae9b6633}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple.selected[_ngcontent-%COMP%]   .quotation-number[_ngcontent-%COMP%]{color:#9b8a5a;font-weight:600}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]:last-child{border-bottom:none}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column[_ngcontent-%COMP%]{display:flex;align-items:center}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.quotation-number-col[_ngcontent-%COMP%]{flex:1.2;min-width:140px}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.quotation-number-col[_ngcontent-%COMP%]   .quotation-number[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#231815}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.create-date-col[_ngcontent-%COMP%]{flex:1.5;min-width:160px}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.create-date-col[_ngcontent-%COMP%]   .create-date[_ngcontent-%COMP%]{font-size:13px;color:#23181599}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.select-col[_ngcontent-%COMP%]{flex:.6;min-width:60px;justify-content:center}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.select-col[_ngcontent-%COMP%]   .version-select-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:24px;height:24px;border-radius:50%;transition:all .3s ease}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.select-col[_ngcontent-%COMP%]   .version-select-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;transition:all .3s ease}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.select-col[_ngcontent-%COMP%]   .version-select-indicator[_ngcontent-%COMP%]   .pi-circle[_ngcontent-%COMP%]{color:#979797}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.select-col[_ngcontent-%COMP%]   .version-select-indicator[_ngcontent-%COMP%]   .pi-check[_ngcontent-%COMP%]{color:#ae9b66}.version-list-simple[_ngcontent-%COMP%]   .version-item-simple[_ngcontent-%COMP%]   .version-column.select-col[_ngcontent-%COMP%]   .version-select-indicator.selected[_ngcontent-%COMP%]{background:#b8a67626;border:2px solid #B8A676}.version-list-simple[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.version-list-simple[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f8f7f4;border-radius:3px}.version-list-simple[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b8a676;border-radius:3px}.version-list-simple[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#ae9b66}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translate(0)}40%{transform:translate(-3px)}60%{transform:translate(-1px)}}@media (max-width: 768px){.version-dialog-content[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]{padding:12px 16px}.version-dialog-content[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .version-item.active[_ngcontent-%COMP%]{padding-left:12px}.version-dialog-content[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]   .version-name[_ngcontent-%COMP%]{font-size:14px}.version-dialog-content[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:8px}}@media print{.wrapper[_ngcontent-%COMP%]{background:#fff!important;-webkit-print-color-adjust:exact;print-color-adjust:exact}.header-actions[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .btn[_ngcontent-%COMP%]{display:none!important}.content[_ngcontent-%COMP%]{max-width:none;margin:0;padding:0}.items-table[_ngcontent-%COMP%], .calculation-table[_ngcontent-%COMP%]{page-break-inside:avoid}.page-title[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:1rem}.section-title[_ngcontent-%COMP%]{font-size:1.2rem}.section-card[_ngcontent-%COMP%]{box-shadow:none!important;border:1px solid #ddd;background:#fff!important}*[_ngcontent-%COMP%]{color:#000!important;background:transparent!important}.table-header[_ngcontent-%COMP%]{background:#f5f5f5!important;color:#000!important}.total-row[_ngcontent-%COMP%]{border-top:2px solid black!important;font-weight:700!important}}@media (max-width: 912px){.quotation-header[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;align-items:stretch}.header-actions[_ngcontent-%COMP%]{justify-content:center}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{border-right:none;border-bottom:1px solid #E5E3E1}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child, .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-bottom:none}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before, .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before{content:attr(data-label) \\\": \\\";font-weight:600;color:#23181599;margin-right:.5rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]{font-size:.8rem;padding:.4rem .6rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{min-width:1rem;font-size:.85rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-label[_ngcontent-%COMP%]{font-size:.75rem}}@media (max-width: 640px){.content[_ngcontent-%COMP%]{padding:1rem}.page-title[_ngcontent-%COMP%]{font-size:1.5rem}.btn[_ngcontent-%COMP%]{min-width:100px;font-size:.75rem}.quotation-info[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]{padding:1rem 1rem 0}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.1rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.35rem .5rem;border-radius:16px}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.7rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{font-size:.8rem;min-width:.8rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-label[_ngcontent-%COMP%]{font-size:.7rem}}.dialog-loading[_ngcontent-%COMP%], .dialog-error[_ngcontent-%COMP%], .dialog-empty[_ngcontent-%COMP%]{padding:40px 20px;text-align:center;color:#23181599}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#b8a676;animation:_ngcontent-%COMP%_spin 1s linear infinite}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;color:#231815}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:32px;color:#f1502f}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .error-text[_ngcontent-%COMP%]{color:#231815;font-size:14px;margin-bottom:8px}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:8px 16px;font-size:14px;border-radius:6px;border:none;cursor:pointer;transition:background-color .3s ease}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);color:#fff;box-shadow:0 2px 8px #ae9b6633}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);box-shadow:0 4px 12px #ae9b664d}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;margin-right:6px}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:32px;color:#23181599}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#23181599;font-size:14px}@media (max-width: 1024px){.quotation-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{padding:1.5rem}.quotation-header[_ngcontent-%COMP%]   .quotation-details-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}.quotation-header[_ngcontent-%COMP%]   .header-actions-container[_ngcontent-%COMP%]{min-width:280px;padding:1.25rem}.quotation-header[_ngcontent-%COMP%]   .header-actions-container[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]{font-size:.9rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{gap:.625rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.625rem 1rem;font-size:.8125rem;min-width:100px}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]{min-width:180px;padding:.875rem 1.25rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{width:36px;height:36px}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.125rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-size:.85rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{font-size:.7rem}}@media (max-width: 480px){.quotation-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{padding:1rem}.quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:1.25rem}.quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]   .number-container[_ngcontent-%COMP%], .quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]   .status-container[_ngcontent-%COMP%]{width:100%;align-items:center;text-align:center}.quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]   .number-container[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%], .quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]   .status-container[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{justify-content:center;margin-bottom:.5rem}.quotation-header[_ngcontent-%COMP%]   .number-badge[_ngcontent-%COMP%]{padding:.625rem 1rem;font-size:1rem}.quotation-header[_ngcontent-%COMP%]   .number-badge[_ngcontent-%COMP%]   .number-text[_ngcontent-%COMP%]{font-size:1rem}.quotation-header[_ngcontent-%COMP%]   .header-actions-container[_ngcontent-%COMP%]{min-width:auto;width:100%;padding:1rem}.quotation-header[_ngcontent-%COMP%]   .header-actions-container[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]{font-size:.85rem;text-align:center}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{width:100%}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{flex:1;min-width:auto}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]{min-width:auto;width:100%;padding:.75rem 1rem;gap:.75rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{width:32px;height:32px}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-size:.8rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{font-size:.65rem}}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_fadeInScale{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.quotation-header[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInScale .6s cubic-bezier(.4,0,.2,1)}.detail-item[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .6s cubic-bezier(.4,0,.2,1)}.detail-item[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.detail-item[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.btn[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .6s cubic-bezier(.4,0,.2,1)}.btn[_ngcontent-%COMP%]:nth-child(1){animation-delay:.3s}.btn[_ngcontent-%COMP%]:nth-child(2){animation-delay:.4s}.btn[_ngcontent-%COMP%]:nth-child(3){animation-delay:.5s}.signature-dialog-content[_ngcontent-%COMP%]{padding:24px;max-width:800px;margin:0 auto;height:85vh;background:#fff;border-radius:16px;display:flex;flex-direction:column}@media screen and (max-width: 768px){.signature-dialog-content[_ngcontent-%COMP%]{padding:16px;max-width:95vw;height:95vh}}.signature-dialog-content[_ngcontent-%COMP%]   .dialog-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;gap:20px;color:#23181599}.signature-dialog-content[_ngcontent-%COMP%]   .dialog-loading[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:32px;color:#b8a676;animation:_ngcontent-%COMP%_spin 1s linear infinite}.signature-dialog-content[_ngcontent-%COMP%]   .dialog-loading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:16px;font-weight:500}.signature-dialog-content[_ngcontent-%COMP%]   .signature-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:18px;font-weight:700;color:#231815;margin-bottom:20px}.signature-dialog-content[_ngcontent-%COMP%]   .signature-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;color:#b8a676}.signature-dialog-content[_ngcontent-%COMP%]   .signature-pad-container[_ngcontent-%COMP%]{flex:1;border-radius:12px;background:#fff;box-shadow:0 2px 8px #00000014;border:1px solid rgba(0,0,0,.1);overflow:hidden;margin-bottom:20px}.signature-dialog-content[_ngcontent-%COMP%]   .signature-pad-container[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #0000001f;border-color:#b8a6764d}.signature-dialog-content[_ngcontent-%COMP%]   .signature-pad-container[_ngcontent-%COMP%]   app-signature-pad[_ngcontent-%COMP%]{display:block;width:100%;height:100%}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]{display:flex;gap:16px;justify-content:center}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:14px 28px;border-radius:12px;border:none;font-weight:600;font-size:15px;cursor:pointer;display:flex;align-items:center;gap:10px;min-width:140px;justify-content:center;transition:all .3s ease}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-outline[_ngcontent-%COMP%]{background:#fff;border:2px solid rgba(0,0,0,.1);color:#23181599}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-outline[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#231815;transform:translateY(-2px);box-shadow:0 4px 12px #0000001a}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 4px 12px #b8a6764d}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 6px 20px #b8a67666}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;background:#9ca3af}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:disabled:hover{transform:none;box-shadow:0 4px 12px #b8a6764d}.signature-dialog-content[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.signature-progress[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);padding:16px 24px;color:#fff}.signature-progress[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin-bottom:12px}.signature-progress[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:18px;font-weight:600}.signature-progress[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px}.signature-progress[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-close[_ngcontent-%COMP%]{background:#fff3;border:none;color:#fff;width:32px;height:32px;border-radius:50%;cursor:pointer;transition:all .2s ease}.signature-progress[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-close[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:scale(1.1)}.signature-progress[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-close[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.signature-progress[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.signature-progress[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:6px 12px;border-radius:20px;background:#ffffff26;font-size:13px;font-weight:500}.signature-progress[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]{background:#22c55e4d}.signature-progress[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]{background:#ffffff40;box-shadow:0 0 0 2px #ffffff4d}.signature-progress[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{width:18px;height:18px;border-radius:50%;background:#ffffff4d;display:flex;align-items:center;justify-content:center}.signature-progress[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.dialog-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;padding:60px 20px;color:#23181599}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:20px;font-size:16px}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spinner-container[_ngcontent-%COMP%]{position:relative;width:60px;height:60px}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spinner-container[_ngcontent-%COMP%]   .spinner-ring[_ngcontent-%COMP%]{position:absolute;width:60px;height:60px;border:3px solid rgba(184,166,118,.3);border-top:3px solid #B8A676;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spinner-container[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-size:24px;color:#b8a676}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;color:#231815}.signature-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]{margin:24px;background:#fff;border-radius:12px;padding:24px;border:1px solid rgba(0,0,0,.1);box-shadow:0 2px 8px #00000014}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:20px;font-weight:700;color:#231815;margin-bottom:20px;padding-bottom:12px;border-bottom:1px solid rgba(0,0,0,.1)}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676;font-size:24px}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-details[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:16px}@media (max-width: 768px){.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-details[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:12px}}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-details[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px;padding:16px;background:#f8fafccc;border-radius:8px;border-left:3px solid #B8A676;transition:all .2s ease}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-details[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:hover{background:#f8fafc;transform:translateY(-1px);box-shadow:0 2px 8px #00000014}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-details[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#23181599;font-size:13px;text-transform:uppercase;letter-spacing:.5px}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-details[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-weight:700;color:#231815;font-size:16px}.signature-content[_ngcontent-%COMP%]   .quotation-summary[_ngcontent-%COMP%]   .summary-details[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .value.amount[_ngcontent-%COMP%]{color:#ae9b66;font-size:20px;text-shadow:0 1px 2px rgba(0,0,0,.1)}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]{margin:0 24px 24px;background:#fff;border-radius:12px;overflow:hidden;border:1px solid rgba(0,0,0,.1);box-shadow:0 2px 8px #00000014}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .terms-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:18px;font-weight:700;color:#fff;margin:0;padding:20px 24px;background:linear-gradient(135deg,#b8a676,#ae9b66)}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .terms-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]{padding:24px;background:transparent}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .terms-list[_ngcontent-%COMP%]{margin:0;padding:0;list-style:none}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .terms-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{position:relative;padding:16px 0 16px 32px;color:#231815;font-size:15px;line-height:1.6;border-bottom:1px solid rgba(0,0,0,.05)}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .terms-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child{border-bottom:none}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .terms-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:0;top:20px;width:8px;height:8px;background:linear-gradient(135deg,#b8a676,#ae9b66);border-radius:50%;box-shadow:0 0 0 3px #b8a67633}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .terms-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover{color:#231815;background:#b8a67605;border-radius:8px;margin:0 -8px;padding-left:40px;padding-right:8px}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .agreement-checkbox[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:20px 24px;background:#f0fdf4cc;border-top:1px solid rgba(34,197,94,.2);transition:all .3s ease}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .agreement-checkbox[_ngcontent-%COMP%]:hover{background:#f0fdf4}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .agreement-checkbox[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{width:20px;height:20px;accent-color:#23A415;cursor:pointer;transform:scale(1.2)}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .agreement-checkbox[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked{animation:_ngcontent-%COMP%_checkboxPulse .3s ease}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .agreement-checkbox[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;color:#231815;cursor:pointer;-webkit-user-select:none;user-select:none;font-size:15px;flex:1}.signature-content[_ngcontent-%COMP%]   .signature-terms[_ngcontent-%COMP%]   .agreement-checkbox[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]:hover{color:#23a415}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;margin:0 24px 24px}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:18px;font-weight:700;color:#231815;margin-bottom:20px;padding:0}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;color:#b8a676}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-pad-container[_ngcontent-%COMP%]{flex:1;border-radius:12px;background:#fff;position:relative;min-height:300px;transition:all .3s ease;overflow:hidden;box-shadow:0 2px 8px #00000014;border:1px solid rgba(0,0,0,.1)}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-pad-container[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #0000001f;border-color:#b8a6764d}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-pad-container[_ngcontent-%COMP%]   app-signature-pad[_ngcontent-%COMP%]{display:block;width:100%;height:100%;position:relative;z-index:1;border-radius:12px}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]{display:flex;gap:16px;justify-content:center;padding:20px 0 0}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:14px 28px;border-radius:12px;border:none;font-weight:600;font-size:15px;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);display:flex;align-items:center;gap:10px;min-width:140px;justify-content:center;position:relative;overflow:hidden}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:0;height:0;background:#ffffff4d;border-radius:50%;transform:translate(-50%,-50%);transition:all .4s ease}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:before{width:300px;height:300px}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-outline[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffffffe6,#f9fafbe6);border:2px solid rgba(205,205,205,.5);color:#23181599;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-outline[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#fff,#f9fafb);border-color:#b8a676;color:#231815;transform:translateY(-3px);box-shadow:0 8px 25px #00000026}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 6px 20px #b8a67666}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#ae9b66,#9b8a5a);transform:translateY(-3px);box-shadow:0 10px 30px #b8a67680}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;background:linear-gradient(135deg,#9ca3af,#6b7280);box-shadow:none}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:disabled:hover{transform:none;box-shadow:none}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px;transition:transform .3s ease}.signature-content[_ngcontent-%COMP%]   .signature-pad-section[_ngcontent-%COMP%]   .signature-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled)   i[_ngcontent-%COMP%]{transform:scale(1.1)}.btn-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffdd64,#f4d03f)!important;color:#231815!important;border:1px solid rgba(255,221,100,.3)!important;box-shadow:0 4px 16px #ffdd6466!important;transition:all .3s ease!important;position:relative!important;overflow:hidden!important;font-weight:600!important}.btn-warning[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);transition:left .6s ease}.btn-warning[_ngcontent-%COMP%]:hover{background:#ffdd640d!important;border-color:#ffdd6466!important;color:#fdc600!important;transform:translateY(-1px)!important;box-shadow:0 6px 24px #ffdd6480!important}.btn-warning[_ngcontent-%COMP%]:hover:before{left:100%}.btn-warning[_ngcontent-%COMP%]:active{transform:translateY(-1px) scale(.98)!important;box-shadow:0 4px 15px #ff6b3566!important}.btn-warning[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{background:#ffffff40!important;border-radius:50%!important;transition:all .3s ease!important}.btn-warning[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff!important;font-weight:700!important;transition:transform .3s ease!important}.btn-warning[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:#ffffff59!important;transform:scale(1.1)!important}.btn-warning[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transform:scale(1.1)!important}.btn-warning[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-weight:700!important;font-size:16px!important;transition:all .3s ease!important}.btn-warning[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{opacity:.95!important;font-size:13px!important;transition:all .3s ease!important}.btn-warning[_ngcontent-%COMP%]:hover   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{text-shadow:0 1px 3px rgba(0,0,0,.2)!important}.btn-warning[_ngcontent-%COMP%]:hover   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{opacity:1!important}@keyframes _ngcontent-%COMP%_checkboxPulse{0%{transform:scale(1.2)}50%{transform:scale(1.4)}to{transform:scale(1.2)}}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1;transform:translate(-50%,-50%) scale(1)}50%{opacity:.8;transform:translate(-50%,-50%) scale(1.05)}to{opacity:1;transform:translate(-50%,-50%) scale(1)}}@keyframes _ngcontent-%COMP%_pulse-signature{0%{transform:scale(1);opacity:0}50%{transform:scale(1.05);opacity:.3}to{transform:scale(1.1);opacity:0}}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_slideInFromTop{0%{opacity:0;transform:translateY(-30px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["signal", "CommonModule", "FormsModule", "EnumStatusCode", "DialogPopupComponent", "SignaturePadComponent", "QUOTATION_TEMPLATE", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "QuotationComponent_div_3_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "loadQuotationHistory", "ɵɵadvance", "ɵɵtextInterpolate1", "error", "QuotationComponent_div_4_div_3_Template_button_click_11_listener", "_r3", "ɵɵtextInterpolate", "selectedVersion", "CversionNo", "ɵɵproperty", "getStatusClass", "getStatusText", "ɵɵpipeBind2", "getCreateDate", "CTotalAmount", "QuotationComponent_div_4_div_4_button_13_Template_button_click_0_listener", "_r5", "openSignatureDialog", "QuotationComponent_div_4_div_4_button_14_Template_button_click_0_listener", "_r6", "viewContract", "QuotationComponent_div_4_div_4_div_18_Template_button_click_5_listener", "_r7", "QuotationComponent_div_4_div_4_div_20_div_19_Template_div_click_0_listener", "version_r9", "_r8", "$implicit", "selectVersion", "ɵɵtemplate", "QuotationComponent_div_4_div_4_div_20_div_19_i_10_Template", "QuotationComponent_div_4_div_4_div_20_div_19_i_11_Template", "ɵɵclassProp", "QuotationComponent_div_4_div_4_div_20_div_19_Template", "quotationVersions", "length", "QuotationComponent_div_4_div_4_div_24_Template_app_signature_pad_signatureData_6_listener", "$event", "_r10", "handleSignatureData", "QuotationComponent_div_4_div_4_div_24_Template_button_click_9_listener", "clearSignature", "QuotationComponent_div_4_div_4_div_24_Template_button_click_12_listener", "confirmSignature", "hasValidSignature", "tmp_4_0", "tblQuotationItems", "i_r12", "item_r11", "CItemName", "CUnit", "CCount", "CUnitPrice", "CSubtotal", "CRemark", "QuotationComponent_div_4_div_4_div_26_div_8_Template", "QuotationComponent_div_4_div_4_div_26_div_30_Template", "tmp_3_0", "ɵɵtextInterpolate2", "COtherName", "COtherPercent", "calculateOtherFee", "QuotationComponent_div_4_div_4_div_3_Template", "QuotationComponent_div_4_div_4_Template_button_click_5_listener", "_r4", "toggleVersionHistory", "QuotationComponent_div_4_div_4_Template_button_click_9_listener", "printPreview", "QuotationComponent_div_4_div_4_button_13_Template", "QuotationComponent_div_4_div_4_button_14_Template", "ɵɵtwoWayListener", "QuotationComponent_div_4_div_4_Template_app_dialog_popup_visibleChange_15_listener", "ɵɵtwoWayBindingSet", "showVersionHistory", "QuotationComponent_div_4_div_4_div_17_Template", "QuotationComponent_div_4_div_4_div_18_Template", "QuotationComponent_div_4_div_4_div_19_Template", "QuotationComponent_div_4_div_4_div_20_Template", "QuotationComponent_div_4_div_4_Template_app_dialog_popup_visibleChange_21_listener", "showSignatureDialog", "QuotationComponent_div_4_div_4_div_23_Template", "QuotationComponent_div_4_div_4_div_24_Template", "QuotationComponent_div_4_div_4_div_26_Template", "QuotationComponent_div_4_div_4_div_42_Template", "convertStatusFromAPI", "CQuotationStatus", "ɵɵtwoWayProperty", "versionHistoryTextData", "isLoading", "ɵɵpureFunction0", "_c1", "isSignatureLoading", "calculateSubtotal", "CShowOther", "calculateTotalWithOther", "QuotationComponent_div_4_div_3_Template", "QuotationComponent_div_4_div_4_Template", "QuotationComponent", "constructor", "quotationService", "signatureData", "title", "header", "content", "titleButtonLeft", "titleButtonRight", "showCloseButton", "ngOnInit", "set", "apiQuotationGetQuotationHistoryPost$Json", "subscribe", "next", "response", "Entries", "console", "version", "onVersionHistoryClose", "status", "getStatusIcon", "printWindow", "window", "open", "templateContent", "generatePrintTemplate", "document", "write", "close", "onload", "print", "template", "items", "subtotal", "otherFee", "totalAmount", "itemsHtml", "map", "item", "index", "toLocaleString", "join", "additionalFeeHtml", "replace", "Date", "toLocaleDateString", "viewVersionDetails", "detailsElement", "querySelector", "scrollIntoView", "behavior", "block", "compareVersion", "alert", "reviseQuotation", "statusCode", "getQuotationNumber", "CQuotationVersionID", "getVersionNumber", "CCreateDT", "reduce", "sum", "calculateTax", "Math", "round", "otherPercent", "log", "currentStatus", "signatureDataUrl", "substring", "startsWith", "warn", "signaturePad", "clear", "signatureRequest", "CQuotationVersionId", "cSign", "apiQuotationSignQuotationPost$Json", "body", "StatusCode", "$0", "handleSignatureSuccess", "errorMessage", "Message", "handleSignatureError", "currentVersion", "message", "_", "ɵɵdirectiveInject", "i1", "QuotationService", "_2", "selectors", "viewQuery", "QuotationComponent_Query", "rf", "ctx", "QuotationComponent_div_2_Template", "QuotationComponent_div_3_Template", "QuotationComponent_div_4_Template", "i2", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "styles", "changeDetection"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\quotation\\quotation.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\quotation\\quotation.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, OnInit, signal, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { QuotationService } from '../../../services/api/services/quotation.service';\r\nimport { GetQuotationVersionsListResponseBase } from '../../../services/api/models/get-quotation-versions-list-response-base';\r\nimport { GetQuotationVersions } from '../../../services/api/models/get-quotation-versions';\r\nimport { TblQuotationItem } from '../../../services/api/models/tbl-quotation-item';\r\nimport { SignQuotation } from '../../../services/api/models/sign-quotation';\r\nimport { StringResponseBase } from '../../../services/api/models/string-response-base';\r\nimport { EnumStatusCode } from '../../../services/api/models/enum-status-code';\r\nimport { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';\r\nimport { SignaturePadComponent } from '../../components/signature-pad/signature-pad.component';\r\nimport { QUOTATION_TEMPLATE } from '../../../assets/template/quotation-template';\r\nimport { ContentDialog } from '../../../model/choice.model';\r\n\r\n@Component({\r\n  selector: 'app-quotation',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, DialogPopupComponent, SignaturePadComponent],\r\n  templateUrl: './quotation.component.html',\r\n  styleUrl: './quotation.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class QuotationComponent implements OnInit {\r\n  @ViewChild('signaturePad') signaturePad!: SignaturePadComponent;\r\n\r\n  quotationVersions = signal<GetQuotationVersions[]>([]);\r\n  selectedVersion = signal<GetQuotationVersions | null>(null);\r\n  showVersionHistory = signal(false);\r\n  isLoading = signal(false);\r\n  error = signal<string | null>(null);\r\n\r\n  // 簽署相關屬性\r\n  showSignatureDialog = signal(false);\r\n  isSignatureLoading = signal(false);\r\n  hasValidSignature = signal(false);\r\n  signatureData = signal<string | null>(null);\r\n\r\n  // 版本歷程 Dialog 的 textData\r\n  versionHistoryTextData: ContentDialog = {\r\n    title: 'versionHistory',\r\n    header: '版本歷程',\r\n    content: '',\r\n    titleButtonLeft: '',\r\n    titleButtonRight: '',\r\n    showCloseButton: true\r\n  };\r\n\r\n  constructor(private quotationService: QuotationService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadQuotationHistory();\r\n  }\r\n\r\n  loadQuotationHistory() {\r\n    this.isLoading.set(true);\r\n    this.error.set(null);\r\n\r\n    this.quotationService.apiQuotationGetQuotationHistoryPost$Json()\r\n      .subscribe({\r\n        next: (response: GetQuotationVersionsListResponseBase) => {\r\n          if (response.Entries) {\r\n            this.quotationVersions.set(response.Entries);\r\n            if (response.Entries.length > 0) {\r\n              this.selectedVersion.set(response.Entries[0]); // 選擇第一個版本\r\n            }\r\n          } else {\r\n            this.quotationVersions.set([]); // 確保設置為空陣列\r\n          }\r\n          this.isLoading.set(false);\r\n        },\r\n        error: (error) => {\r\n          console.error('載入報價歷程失敗:', error);\r\n          this.error.set('載入報價歷程失敗，請稍後再試。');\r\n          this.quotationVersions.set([]); // 確保錯誤時清空資料\r\n          this.selectedVersion.set(null);\r\n          this.isLoading.set(false);\r\n        }\r\n      });\r\n  }\r\n  selectVersion(version: GetQuotationVersions) {\r\n    this.selectedVersion.set(version);\r\n    this.showVersionHistory.set(false); // 選擇後關閉 dialog\r\n  }\r\n\r\n  toggleVersionHistory() {\r\n    this.showVersionHistory.set(!this.showVersionHistory());\r\n  }\r\n\r\n  onVersionHistoryClose() {\r\n    this.showVersionHistory.set(false);\r\n  }\r\n\r\n  // 輔助方法來獲取狀態樣式類別\r\n  getStatusClass(version: GetQuotationVersions): string {\r\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\r\n    switch (status) {\r\n      case 'pending': return 'status-pending';\r\n      case 'quoted': return 'status-quoted';\r\n      case 'confirmed': return 'status-confirmed';\r\n      case 'sent': return 'status-sent';\r\n      case 'approved': return 'status-approved';\r\n      case 'rejected': return 'status-rejected';\r\n      case 'expired': return 'status-expired';\r\n      default: return 'status-pending';\r\n    }\r\n  }\r\n\r\n  // 輔助方法來獲取狀態圖示\r\n  getStatusIcon(version: GetQuotationVersions): string {\r\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\r\n    switch (status) {\r\n      case 'pending': return 'icon-clock';\r\n      case 'quoted': return 'icon-file-text';\r\n      case 'confirmed': return 'icon-check-circle';\r\n      case 'sent': return 'icon-send';\r\n      case 'approved': return 'icon-check-circle-2';\r\n      case 'rejected': return 'icon-x-circle';\r\n      case 'expired': return 'icon-alert-triangle';\r\n      default: return 'icon-clock';\r\n    }\r\n  }\r\n\r\n  // 輔助方法來獲取狀態文字\r\n  getStatusText(version: GetQuotationVersions): string {\r\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\r\n    switch (status) {\r\n      case 'pending': return '待報價';\r\n      case 'quoted': return '已報價';\r\n      case 'confirmed': return '已簽回';\r\n      case 'sent': return '已發送';\r\n      case 'approved': return '已核准';\r\n      case 'rejected': return '已拒絕';\r\n      case 'expired': return '已過期';\r\n      default: return '待報價';\r\n    }\r\n  }\r\n\r\n  printPreview() {\r\n    // 創建新視窗顯示套印模板\r\n    const printWindow = window.open('', '_blank', 'width=800,height=600');\r\n    if (printWindow && this.selectedVersion()) {\r\n      const templateContent = this.generatePrintTemplate();\r\n      printWindow.document.write(templateContent);\r\n      printWindow.document.close();\r\n\r\n      // 等待內容載入後執行列印\r\n      printWindow.onload = () => {\r\n        printWindow.print();\r\n        printWindow.close();\r\n      };\r\n    }\r\n  }\r\n\r\n  private generatePrintTemplate(): string {\r\n    const version = this.selectedVersion();\r\n    if (!version) return '';\r\n\r\n    // 使用新的模板\r\n    let template = QUOTATION_TEMPLATE;\r\n\r\n    // 準備模板變數\r\n    const items = version.tblQuotationItems || [];\r\n    const subtotal = this.calculateSubtotal(items);\r\n    const otherFee = version.CShowOther ? this.calculateOtherFee(items, version.COtherPercent) : 0;\r\n    const totalAmount = this.calculateTotalWithOther(version);\r\n\r\n    // 生成項目HTML\r\n    const itemsHtml = items.map((item, index) => `\r\n      <tr>\r\n        <td class=\"text-center\">${index + 1}</td>\r\n        <td>${item.CItemName || ''}</td>\r\n        <td class=\"text-right\">NT$ ${(item.CUnitPrice || 0).toLocaleString()}</td>\r\n        <td class=\"text-center\">${item.CUnit || ''}</td>\r\n        <td class=\"text-center\">${item.CCount || 0}</td>\r\n        <td class=\"text-right\">NT$ ${(item.CSubtotal || 0).toLocaleString()}</td>\r\n        <td>${item.CRemark || ''}</td>\r\n      </tr>\r\n    `).join('');\r\n\r\n    // 生成額外費用HTML\r\n    const additionalFeeHtml = version.CShowOther && version.COtherPercent ?\r\n      `<div class=\"additional-fee\">${version.COtherName || '額外費用'} (${version.COtherPercent}%)：NT$ ${otherFee.toLocaleString()}</div>` : '';\r\n\r\n    // 替換模板變數\r\n    template = template\r\n      .replace(/{{buildCaseName}}/g, '建案名稱') // 這裡可以根據實際需求調整\r\n      .replace(/{{houseHold}}/g, '戶別資訊') // 這裡可以根據實際需求調整\r\n      .replace(/{{floor}}/g, '樓層') // 這裡可以根據實際需求調整\r\n      .replace(/{{printDate}}/g, new Date().toLocaleDateString('zh-TW'))\r\n      .replace(/{{itemsHtml}}/g, itemsHtml)\r\n      .replace(/{{subtotalAmount}}/g, `NT$ ${subtotal.toLocaleString()}`)\r\n      .replace(/{{additionalFeeHtml}}/g, additionalFeeHtml)\r\n      .replace(/{{totalAmount}}/g, `NT$ ${totalAmount.toLocaleString()}`)\r\n      .replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\r\n\r\n    return template;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n  viewVersionDetails(version: GetQuotationVersions) {\r\n    this.selectedVersion.set(version);\r\n    // 可以添加額外的檢視邏輯，如滾動到詳情區域\r\n    const detailsElement = document.querySelector('.quotation-details');\r\n    if (detailsElement) {\r\n      detailsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });\r\n    }\r\n  }\r\n\r\n  compareVersion(version: GetQuotationVersions) {\r\n    // TODO: 實作版本比較功能\r\n    alert(`比較版本 ${version.CversionNo} 與當前版本 ${this.selectedVersion()?.CversionNo} 的功能開發中...`);\r\n  }\r\n\r\n  reviseQuotation() {\r\n    // TODO: 實作修改報價功能\r\n    alert('修改報價功能開發中...');\r\n  }\r\n\r\n  viewContract() {\r\n    // TODO: 實作查看合約功能\r\n    alert('查看合約功能開發中...');\r\n  }\r\n\r\n  convertStatusFromAPI(statusCode?: number): 'pending' | 'quoted' | 'confirmed' | 'sent' | 'approved' | 'rejected' | 'expired' {\r\n    switch (statusCode) {\r\n      case 1: return 'pending';  // 待報價\r\n      case 2: return 'quoted';   // 已報價\r\n      case 3: return 'confirmed'; // 已簽回\r\n      default: return 'pending'; // 預設為待報價\r\n    }\r\n  }\r\n\r\n  // 輔助方法來獲取報價編號\r\n  getQuotationNumber(version: GetQuotationVersions): string {\r\n    return `Q${version.CQuotationVersionID}`;\r\n  }\r\n\r\n  // 輔助方法來獲取版本號\r\n  getVersionNumber(version: GetQuotationVersions): string {\r\n    return version.CversionNo || 'v1.0';\r\n  }\r\n\r\n  // 輔助方法來獲取建立日期\r\n  getCreateDate(version: GetQuotationVersions): Date {\r\n    return version.CCreateDT ? new Date(version.CCreateDT) : new Date();\r\n  }\r\n\r\n  // 輔助方法來計算小計\r\n  calculateSubtotal(items?: TblQuotationItem[] | null): number {\r\n    if (!items) return 0;\r\n    return items.reduce((sum, item) => sum + (item.CSubtotal || 0), 0);\r\n  }\r\n\r\n  // 輔助方法來計算稅額\r\n  calculateTax(items?: TblQuotationItem[] | null): number {\r\n    const subtotal = this.calculateSubtotal(items);\r\n    return Math.round(subtotal * 0.1); // 10% 稅率\r\n  }\r\n\r\n  // 輔助方法來計算額外費用\r\n  calculateOtherFee(items?: TblQuotationItem[] | null, otherPercent?: number): number {\r\n    if (!otherPercent || otherPercent <= 0) return 0;\r\n    const subtotal = this.calculateSubtotal(items);\r\n    return Math.round(subtotal * (otherPercent / 100));\r\n  }\r\n\r\n  // 輔助方法來計算含額外費用的總計\r\n  calculateTotalWithOther(version: GetQuotationVersions): number {\r\n    const subtotal = this.calculateSubtotal(version.tblQuotationItems);\r\n    const otherFee = version.CShowOther ? this.calculateOtherFee(version.tblQuotationItems, version.COtherPercent) : 0;\r\n    return subtotal + otherFee;\r\n  }\r\n\r\n  // ===== 簽署相關方法 =====\r\n\r\n  // 開啟簽署對話框\r\n  openSignatureDialog(): void {\r\n    console.log('開啟簽署對話框');\r\n    console.log('當前版本:', this.selectedVersion());\r\n    console.log('當前狀態:', this.selectedVersion()?.CQuotationStatus);\r\n    console.log('轉換後狀態:', this.convertStatusFromAPI(this.selectedVersion()?.CQuotationStatus));\r\n\r\n    // 檢查是否有選中的版本\r\n    if (!this.selectedVersion()) {\r\n      alert('請先選擇一個報價版本');\r\n      return;\r\n    }\r\n\r\n    // 檢查版本狀態是否允許簽署\r\n    const currentStatus = this.convertStatusFromAPI(this.selectedVersion()?.CQuotationStatus);\r\n    if (currentStatus === 'confirmed') {\r\n      alert('此報價單已經簽署過了');\r\n      return;\r\n    }\r\n\r\n    this.showSignatureDialog.set(true);\r\n    this.hasValidSignature.set(false);\r\n    this.signatureData.set(null);\r\n  }\r\n\r\n  // 處理簽名資料\r\n  handleSignatureData(signatureDataUrl: string | null): void {\r\n    console.log('收到簽名資料:', signatureDataUrl?.substring(0, 50) + '...');\r\n\r\n    this.signatureData.set(signatureDataUrl);\r\n    this.hasValidSignature.set(!!signatureDataUrl && signatureDataUrl.length > 0);\r\n\r\n    // 驗證簽名資料格式 (應該是 data:image/png;base64,... 格式)\r\n    if (signatureDataUrl && !signatureDataUrl.startsWith('data:image/')) {\r\n      console.warn('簽名資料格式可能不正確:', signatureDataUrl.substring(0, 100));\r\n    }\r\n  }\r\n\r\n  // 清除簽名\r\n  clearSignature(): void {\r\n    if (this.signaturePad) {\r\n      this.signaturePad.clear();\r\n      this.hasValidSignature.set(false);\r\n      this.signatureData.set(null);\r\n    }\r\n  }\r\n\r\n  // 確認簽署\r\n  confirmSignature(): void {\r\n    if (!this.hasValidSignature() || !this.selectedVersion()) {\r\n      return;\r\n    }\r\n\r\n    this.isSignatureLoading.set(true);\r\n\r\n    // 準備簽署資料\r\n    const signatureRequest: SignQuotation = {\r\n      CQuotationVersionId: this.selectedVersion()!.CQuotationVersionID,\r\n      cSign: this.signatureData()\r\n    };\r\n\r\n    // 呼叫實際的簽署 API\r\n    this.quotationService.apiQuotationSignQuotationPost$Json({ body: signatureRequest })\r\n      .subscribe({\r\n        next: (response: StringResponseBase) => {\r\n          console.log('簽署 API 回應:', response);\r\n\r\n          // 檢查回應狀態碼 (0 通常代表成功)\r\n          if (response.StatusCode === EnumStatusCode.$0) {\r\n            this.handleSignatureSuccess();\r\n          } else {\r\n            const errorMessage = response.Message || `簽署失敗 (狀態碼: ${response.StatusCode})`;\r\n            this.handleSignatureError(errorMessage);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('簽署 API 錯誤:', error);\r\n          this.handleSignatureError(error);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 處理簽署成功\r\n  private handleSignatureSuccess(): void {\r\n    this.isSignatureLoading.set(false);\r\n    this.showSignatureDialog.set(false);\r\n\r\n    // 更新報價單狀態為已簽回\r\n    const currentVersion = this.selectedVersion();\r\n    if (currentVersion) {\r\n      // 更新狀態 (實際情況下這會從 API 回應中取得)\r\n      currentVersion.CQuotationStatus = 3; // 3 = confirmed (已簽回)\r\n      this.selectedVersion.set({ ...currentVersion });\r\n    }\r\n\r\n    // 重新載入資料以確保狀態同步\r\n    this.loadQuotationHistory();\r\n\r\n    // 顯示成功訊息\r\n    alert('簽署成功！報價單已確認。');\r\n  }\r\n\r\n  // 處理簽署錯誤\r\n  private handleSignatureError(error: any): void {\r\n    this.isSignatureLoading.set(false);\r\n    console.error('簽署失敗:', error);\r\n\r\n    let errorMessage = '簽署失敗，請稍後再試。';\r\n\r\n    // 根據錯誤類型設定不同的錯誤訊息\r\n    if (typeof error === 'string') {\r\n      errorMessage = error;\r\n    } else if (error?.message) {\r\n      errorMessage = error.message;\r\n    } else if (error?.error?.message) {\r\n      errorMessage = error.error.message;\r\n    }\r\n\r\n    alert(errorMessage);\r\n  }\r\n\r\n}\r\n", "<div class=\"wrapper\">\r\n  <div class=\"content\">\r\n\r\n    <!-- 載入狀態 -->\r\n    <div *ngIf=\"isLoading()\" class=\"loading-container\">\r\n      <div class=\"loading-spinner\">載入中...</div>\r\n    </div>\r\n\r\n    <!-- 錯誤狀態 -->\r\n    <div *ngIf=\"error()\" class=\"error-container\">\r\n      <div class=\"error-message\">\r\n        <i class=\"icon-alert\"></i>\r\n        {{ error() }}\r\n      </div>\r\n      <button class=\"btn btn-primary\" (click)=\"loadQuotationHistory()\">重新載入</button>\r\n    </div>\r\n\r\n    <!-- 主要內容 - 只有在沒有載入且沒有錯誤時顯示 -->\r\n    <div *ngIf=\"!isLoading() && !error()\">\r\n\r\n      <!-- 頁面標題 - 簡化 -->\r\n      <div class=\"page-title\">報價單管理</div>\r\n\r\n      <!-- 無報價單時的空狀態 -->\r\n      <div *ngIf=\"quotationVersions().length === 0\" class=\"empty-state\">\r\n        <div class=\"empty-state-content\">\r\n          <div class=\"empty-icon\">\r\n            <i class=\"icon-file-text\"></i>\r\n          </div>\r\n          <h3 class=\"empty-title\">暫無報價單記錄</h3>\r\n          <p class=\"empty-description\">\r\n            目前系統中沒有任何報價單，<br>\r\n            請確認是否有報價單資料或嘗試重新載入\r\n          </p>\r\n          <div class=\"empty-actions\">\r\n            <button class=\"btn btn-primary\" (click)=\"loadQuotationHistory()\">\r\n              <i class=\"icon-refresh-cw\"></i>\r\n              重新載入資料\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 有報價單時顯示的內容 -->\r\n      <div *ngIf=\"quotationVersions().length > 0\">\r\n        <!-- 緊湊型報價單頭部區域 -->\r\n        <div class=\"quotation-header-compact\">\r\n          <div class=\"quotation-summary-card\">\r\n            <!-- 基本資訊 - 水平排列 -->\r\n            <div class=\"basic-info-row\" *ngIf=\"selectedVersion()\">\r\n              <div class=\"info-group\">\r\n                <span class=\"info-label\">\r\n                  <i class=\"pi pi-file\"></i>\r\n                  報價單號\r\n                </span>\r\n                <span class=\"info-value\">{{ selectedVersion()!.CversionNo }}</span>\r\n              </div>\r\n              <div class=\"info-group\">\r\n                <span class=\"info-label\">\r\n                  <i class=\"pi pi-info-circle\"></i>\r\n                  狀態\r\n                </span>\r\n                <span class=\"info-value\">\r\n                  <span class=\"compact\" [ngClass]=\"getStatusClass(selectedVersion()!)\">\r\n                    {{ getStatusText(selectedVersion()!) }}\r\n                  </span>\r\n                </span>\r\n              </div>\r\n              <div class=\"info-group\">\r\n                <span class=\"info-label\">\r\n                  <i class=\"pi pi-calendar\"></i>\r\n                  建立日期\r\n                </span>\r\n                <span class=\"info-value\">{{ getCreateDate(selectedVersion()!) | date:'yyyy/MM/dd' }}</span>\r\n              </div>\r\n              <div class=\"info-group amount-group\">\r\n                <span class=\"info-label\">\r\n                  <i class=\"pi pi-dollar\"></i>\r\n                  總金額\r\n                </span>\r\n                <span class=\"info-value amount\">NT$ {{ selectedVersion()!.CTotalAmount | number:'1.0-0' }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 操作按鈕 - 水平排列 -->\r\n            <div class=\"action-buttons-row\">\r\n              <button class=\"btn-compact btn-outline\" (click)=\"toggleVersionHistory()\">\r\n                <i class=\"pi pi-history\"></i>\r\n                <span>版本歷程</span>\r\n              </button>\r\n              <button class=\"btn-compact btn-primary\" (click)=\"printPreview()\">\r\n                <i class=\"pi pi-print\"></i>\r\n                <span>預覽列印</span>\r\n              </button>\r\n              <button class=\"btn-compact btn-warning\" (click)=\"openSignatureDialog()\"\r\n                *ngIf=\"selectedVersion() && convertStatusFromAPI(selectedVersion()!.CQuotationStatus) !== 'confirmed'\">\r\n                <i class=\"pi pi-pencil\"></i>\r\n                <span>線上簽署</span>\r\n              </button>\r\n              <button class=\"btn-compact btn-success\" (click)=\"viewContract()\"\r\n                *ngIf=\"selectedVersion() && convertStatusFromAPI(selectedVersion()!.CQuotationStatus) === 'confirmed'\">\r\n                <i class=\"pi pi-file\"></i>\r\n                <span>查看合約</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 版本歷程 Dialog -->\r\n        <app-dialog-popup [(visible)]=\"showVersionHistory\" [textData]=\"versionHistoryTextData\">\r\n\r\n          <div class=\"version-dialog-content\">\r\n            <!-- 載入狀態 -->\r\n            <div *ngIf=\"isLoading()\" class=\"dialog-loading\">\r\n              <div class=\"loading-spinner\">\r\n                <i class=\"icon-loader\"></i>\r\n                <span>載入版本資料中...</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 錯誤狀態 -->\r\n            <div *ngIf=\"error()\" class=\"dialog-error\">\r\n              <div class=\"error-content\">\r\n                <i class=\"icon-alert-triangle\"></i>\r\n                <span class=\"error-text\">{{ error() }}</span>\r\n                <button class=\"btn btn-primary\" (click)=\"loadQuotationHistory()\">\r\n                  <i class=\"icon-refresh-cw\"></i>\r\n                  重新載入\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"!isLoading() && !error() && quotationVersions().length === 0\" class=\"dialog-empty\">\r\n              <div class=\"empty-content\">\r\n                <i class=\"icon-file-text\"></i>\r\n                <span>暫無版本記錄</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 版本清單 - 重新設計UI -->\r\n            <div *ngIf=\"!isLoading() && !error() && quotationVersions().length > 0\" class=\"version-list-simple\">\r\n              <!-- 版本清單標題 -->\r\n              <div class=\"version-list-header\">\r\n                <h4 class=\"version-list-title\">\r\n                  <i class=\"pi pi-list\"></i>\r\n                  版本清單\r\n                </h4>\r\n                <div class=\"version-count\">\r\n                  共 {{ quotationVersions().length }} 個版本\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 欄位標題 -->\r\n              <div class=\"version-table-header\">\r\n                <div class=\"header-column quotation-number-col\">\r\n                  <i class=\"pi pi-file\"></i>\r\n                  <span>報價單號</span>\r\n                </div>\r\n                <div class=\"header-column create-date-col\">\r\n                  <i class=\"pi pi-calendar\"></i>\r\n                  <span>建立日期</span>\r\n                </div>\r\n                <div class=\"header-column select-col\">\r\n                  <span>選擇</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 版本項目 -->\r\n              <div class=\"version-item-simple\" *ngFor=\"let version of quotationVersions(); let i = index\"\r\n                [class.selected]=\"selectedVersion() === version\" (click)=\"selectVersion(version)\">\r\n\r\n                <div class=\"version-column quotation-number-col\">\r\n                  <span class=\"quotation-number\">{{ version.CversionNo }}</span>\r\n                </div>\r\n\r\n                <div class=\"version-column create-date-col\">\r\n                  <span class=\"create-date\">{{ getCreateDate(version) | date:'yyyy/MM/dd HH:mm' }}</span>\r\n                </div>\r\n\r\n                <div class=\"version-column select-col\">\r\n                  <div class=\"version-select-indicator\" [class.selected]=\"selectedVersion() === version\">\r\n                    <i class=\"pi pi-check\" *ngIf=\"selectedVersion() === version\"></i>\r\n                    <i class=\"pi pi-circle\" *ngIf=\"selectedVersion() !== version\"></i>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </app-dialog-popup>\r\n\r\n        <!-- 簽署確認 Dialog -->\r\n        <app-dialog-popup [(visible)]=\"showSignatureDialog\"\r\n          [textData]=\"{header: '電子簽名', content: '', titleButtonLeft: '', titleButtonRight: ''}\">\r\n\r\n          <div class=\"signature-dialog-content\">\r\n            <!-- 載入狀態 -->\r\n            <div *ngIf=\"isSignatureLoading()\" class=\"dialog-loading\">\r\n              <div class=\"loading-spinner\">\r\n                <i class=\"icon-loader\"></i>\r\n                <span>處理簽署中...</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 簽名內容 -->\r\n            <div *ngIf=\"!isSignatureLoading()\" class=\"signature-content\">\r\n              <!-- 電子簽名區 -->\r\n              <div class=\"signature-pad-section\">\r\n                <h4 class=\"signature-title\">\r\n                  <i class=\"pi pi-pencil\"></i>\r\n                  請在下方簽名\r\n                </h4>\r\n                <div class=\"signature-pad-container\">\r\n                  <app-signature-pad #signaturePad (signatureData)=\"handleSignatureData($event)\">\r\n                  </app-signature-pad>\r\n                </div>\r\n                <div class=\"signature-actions\">\r\n                  <button class=\"btn btn-outline\" (click)=\"clearSignature()\">\r\n                    <i class=\"pi pi-refresh\"></i>\r\n                    重新簽名\r\n                  </button>\r\n                  <button class=\"btn btn-primary\" (click)=\"confirmSignature()\" [disabled]=\"!hasValidSignature()\">\r\n                    <i class=\"pi pi-check\"></i>\r\n                    確認簽署\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </app-dialog-popup>\r\n\r\n        <!-- 主要內容區域 -->\r\n        <div class=\"quotation-content\">\r\n          <div class=\"quotation-details\" *ngIf=\"selectedVersion()\">\r\n\r\n            <!-- 報價項目 -->\r\n            <div class=\"section items-section\">\r\n              <div class=\"section-card\">\r\n                <div class=\"section-header\">\r\n                  <div class=\"section-title-wrapper\">\r\n                    <h2 class=\"section-title\">\r\n                      <i class=\"icon-list\"></i>\r\n                      報價項目\r\n                    </h2>\r\n                    <div class=\"items-count-badge\" *ngIf=\"selectedVersion()?.tblQuotationItems\">\r\n                      <i class=\"icon-hash\"></i>\r\n                      <span class=\"count-text\">{{ selectedVersion()?.tblQuotationItems?.length || 0 }}</span>\r\n                      <span class=\"count-label\">個項目</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"items-table\">\r\n                  <div class=\"table-header\">\r\n                    <div class=\"col-item\">\r\n                      <i class=\"icon-package\"></i>\r\n                      項目名稱\r\n                    </div>\r\n                    <div class=\"col-unit\">\r\n                      <i class=\"icon-tag\"></i>\r\n                      單位\r\n                    </div>\r\n                    <div class=\"col-qty\">\r\n                      <i class=\"icon-hash\"></i>\r\n                      數量\r\n                    </div>\r\n                    <div class=\"col-price\">\r\n                      <i class=\"icon-dollar-sign\"></i>\r\n                      單價\r\n                    </div>\r\n                    <div class=\"col-total\">\r\n                      <i class=\"icon-calculator\"></i>\r\n                      小計\r\n                    </div>\r\n                    <div class=\"col-remark\">\r\n                      <i class=\"icon-message-square\"></i>\r\n                      備註\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"table-body\">\r\n                    <div class=\"table-row\" *ngFor=\"let item of selectedVersion()?.tblQuotationItems; let i = index\"\r\n                      [class.row-even]=\"i % 2 === 0\">\r\n                      <div class=\"col-item\">\r\n                        <div class=\"item-info\">\r\n                          <span class=\"item-name\">{{ item.CItemName }}</span>\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"col-unit\">\r\n                        <span class=\"unit-value\">{{ item.CUnit || '-' }}</span>\r\n                      </div>\r\n                      <div class=\"col-qty\">\r\n                        <span class=\"qty-value\">{{ item.CCount }}</span>\r\n                      </div>\r\n                      <div class=\"col-price\">\r\n                        <span class=\"price-value\">NT$ {{ item.CUnitPrice | number:'1.0-0' }}</span>\r\n                      </div>\r\n                      <div class=\"col-total\">\r\n                        <span class=\"total-value\">NT$ {{ item.CSubtotal | number:'1.0-0' }}</span>\r\n                      </div>\r\n                      <div class=\"col-remark\">\r\n                        <span class=\"remark-value\">{{ item.CRemark || '-' }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 金額計算 -->\r\n          <div class=\"section calculation-section\">\r\n            <div class=\"section-card\">\r\n              <div class=\"section-header\">\r\n                <h2 class=\"section-title\">\r\n                  <i class=\"icon-calculator\"></i>\r\n                  金額明細\r\n                </h2>\r\n              </div>\r\n              <div class=\"calculation-table\">\r\n                <!-- 小計 -->\r\n                <div class=\"calc-row subtotal-row\">\r\n                  <div class=\"calc-label\">\r\n                    <i class=\"icon-plus\"></i>\r\n                    <span>小計</span>\r\n                  </div>\r\n                  <div class=\"calc-value\">\r\n                    NT$ {{ calculateSubtotal(selectedVersion()!.tblQuotationItems) | number:'1.0-0' }}\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 額外費用區域 (自定義) -->\r\n                <div class=\"calc-row other-fee-row\"\r\n                  *ngIf=\"selectedVersion()!.CShowOther && selectedVersion()!.COtherPercent\">\r\n                  <div class=\"calc-label\">\r\n                    <i class=\"icon-plus-circle\"></i>\r\n                    <span>{{ selectedVersion()!.COtherName || '額外費用' }} ({{ selectedVersion()!.COtherPercent }}%)</span>\r\n                  </div>\r\n                  <div class=\"calc-value\">\r\n                    NT$ {{ calculateOtherFee(selectedVersion()!.tblQuotationItems, selectedVersion()!.COtherPercent) |\r\n                    number:'1.0-0' }}\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 總計 -->\r\n                <div class=\"calc-row total-row\">\r\n                  <div class=\"calc-label\">\r\n                    <i class=\"icon-dollar-sign\"></i>\r\n                    <span>總計</span>\r\n                  </div>\r\n                  <div class=\"calc-value total-amount\">\r\n                    NT$ {{ calculateTotalWithOther(selectedVersion()!) | number:'1.0-0' }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 備註 -->\r\n          <div class=\"section notes-section\">\r\n            <div class=\"section-card\">\r\n              <div class=\"section-header\">\r\n                <h2 class=\"section-title\">\r\n                  <i class=\"icon-info\"></i>\r\n                  重要備註\r\n                </h2>\r\n              </div>\r\n              <div class=\"notes-content\">\r\n                <div class=\"note-category\">\r\n                  <h4 class=\"note-category-title\">\r\n                    <i class=\"icon-clock\"></i>\r\n                    效期與時間\r\n                  </h4>\r\n                  <div class=\"note-item\">\r\n                    <span class=\"note-text\">報價單有效期限為30天</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"note-category\">\r\n                  <h4 class=\"note-category-title\">\r\n                    <i class=\"icon-dollar-sign\"></i>\r\n                    價格說明\r\n                  </h4>\r\n                  <div class=\"note-item\">\r\n                    <span class=\"note-text\">價格含稅</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"note-category\">\r\n                  <h4 class=\"note-category-title\">\r\n                    <i class=\"icon-credit-card\"></i>\r\n                    付款條件\r\n                  </h4>\r\n                  <div class=\"note-item\">\r\n                    <span class=\"note-text\">付款方式：交貨後30天內付款</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"note-category\">\r\n                  <h4 class=\"note-category-title\">\r\n                    <i class=\"icon-help-circle\"></i>\r\n                    聯繫方式\r\n                  </h4>\r\n                  <div class=\"note-item\">\r\n                    <span class=\"note-text\">如有疑問請洽詢業務人員</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div> <!-- 關閉主要內容區塊 -->\r\n\r\n  </div> <!-- 關閉content -->\r\n</div> <!-- 關閉wrapper -->\r\n"], "mappings": "AAAA,SAAqDA,MAAM,QAAmB,eAAe;AAC7F,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAO5C,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,qBAAqB,QAAQ,wDAAwD;AAC9F,SAASC,kBAAkB,QAAQ,6CAA6C;;;;;;;;;;;;;ICP1EC,EADF,CAAAC,cAAA,aAAmD,aACpB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IACrCF,EADqC,CAAAG,YAAA,EAAM,EACrC;;;;;;IAIJH,EADF,CAAAC,cAAA,aAA6C,aAChB;IACzBD,EAAA,CAAAI,SAAA,YAA0B;IAC1BJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAAiE;IAAjCD,EAAA,CAAAK,UAAA,mBAAAC,0DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,oBAAA,EAAsB;IAAA,EAAC;IAACZ,EAAA,CAAAE,MAAA,+BAAI;IACvEF,EADuE,CAAAG,YAAA,EAAS,EAC1E;;;;IAHFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,KAAA,QACF;;;;;;IAaIf,EAFJ,CAAAC,cAAA,cAAkE,cAC/B,cACP;IACtBD,EAAA,CAAAI,SAAA,YAA8B;IAChCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,YAA6B;IAC3BD,EAAA,CAAAE,MAAA,sFAAa;IAAAF,EAAA,CAAAI,SAAA,SAAI;IACjBJ,EAAA,CAAAE,MAAA,qHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,eAA2B,kBACwC;IAAjCD,EAAA,CAAAK,UAAA,mBAAAW,iEAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,oBAAA,EAAsB;IAAA,EAAC;IAC9DZ,EAAA,CAAAI,SAAA,aAA+B;IAC/BJ,EAAA,CAAAE,MAAA,8CACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;IAUIH,EAFJ,CAAAC,cAAA,cAAsD,cAC5B,eACG;IACvBD,EAAA,CAAAI,SAAA,YAA0B;IAC1BJ,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAC/D;IAEJH,EADF,CAAAC,cAAA,cAAwB,eACG;IACvBD,EAAA,CAAAI,SAAA,YAAiC;IACjCJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAELH,EADF,CAAAC,cAAA,gBAAyB,gBAC8C;IACnED,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACF,EACH;IAEJH,EADF,CAAAC,cAAA,eAAwB,gBACG;IACvBD,EAAA,CAAAI,SAAA,aAA8B;IAC9BJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA2D;;IACtFF,EADsF,CAAAG,YAAA,EAAO,EACvF;IAEJH,EADF,CAAAC,cAAA,eAAqC,gBACV;IACvBD,EAAA,CAAAI,SAAA,aAA4B;IAC5BJ,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAE,MAAA,IAA0D;;IAE9FF,EAF8F,CAAAG,YAAA,EAAO,EAC7F,EACF;;;;IA3BuBH,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAkB,iBAAA,CAAAT,MAAA,CAAAU,eAAA,GAAAC,UAAA,CAAmC;IAQpCpB,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAqB,UAAA,YAAAZ,MAAA,CAAAa,cAAA,CAAAb,MAAA,CAAAU,eAAA,IAA8C;IAClEnB,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAc,aAAA,CAAAd,MAAA,CAAAU,eAAA,SACF;IAQuBnB,EAAA,CAAAa,SAAA,GAA2D;IAA3Db,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAwB,WAAA,QAAAf,MAAA,CAAAgB,aAAA,CAAAhB,MAAA,CAAAU,eAAA,mBAA2D;IAOpDnB,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAAwB,WAAA,QAAAf,MAAA,CAAAU,eAAA,GAAAO,YAAA,eAA0D;;;;;;IAc5F1B,EAAA,CAAAC,cAAA,iBACyG;IADjED,EAAA,CAAAK,UAAA,mBAAAsB,0EAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAqB,GAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoB,mBAAA,EAAqB;IAAA,EAAC;IAErE7B,EAAA,CAAAI,SAAA,YAA4B;IAC5BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACZF,EADY,CAAAG,YAAA,EAAO,EACV;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACyG;IADjED,EAAA,CAAAK,UAAA,mBAAAyB,0EAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuB,YAAA,EAAc;IAAA,EAAC;IAE9DhC,EAAA,CAAAI,SAAA,YAA0B;IAC1BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACZF,EADY,CAAAG,YAAA,EAAO,EACV;;;;;IAWTH,EADF,CAAAC,cAAA,cAAgD,aACjB;IAC3BD,EAAA,CAAAI,SAAA,YAA2B;IAC3BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAEpBF,EAFoB,CAAAG,YAAA,EAAO,EACnB,EACF;;;;;;IAIJH,EADF,CAAAC,cAAA,cAA0C,cACb;IACzBD,EAAA,CAAAI,SAAA,YAAmC;IACnCJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,iBAAiE;IAAjCD,EAAA,CAAAK,UAAA,mBAAA4B,uEAAA;MAAAjC,EAAA,CAAAO,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,oBAAA,EAAsB;IAAA,EAAC;IAC9DZ,EAAA,CAAAI,SAAA,YAA+B;IAC/BJ,EAAA,CAAAE,MAAA,iCACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IANuBH,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAkB,iBAAA,CAAAT,MAAA,CAAAM,KAAA,GAAa;;;;;IAUxCf,EADF,CAAAC,cAAA,cAA+F,cAClE;IACzBD,EAAA,CAAAI,SAAA,YAA8B;IAC9BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAEhBF,EAFgB,CAAAG,YAAA,EAAO,EACf,EACF;;;;;IA4CEH,EAAA,CAAAI,SAAA,aAAiE;;;;;IACjEJ,EAAA,CAAAI,SAAA,aAAkE;;;;;;IAdxEJ,EAAA,CAAAC,cAAA,cACoF;IAAjCD,EAAA,CAAAK,UAAA,mBAAA8B,2EAAA;MAAA,MAAAC,UAAA,GAAApC,EAAA,CAAAO,aAAA,CAAA8B,GAAA,EAAAC,SAAA;MAAA,MAAA7B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8B,aAAA,CAAAH,UAAA,CAAsB;IAAA,EAAC;IAG/EpC,EADF,CAAAC,cAAA,cAAiD,gBAChB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC1D;IAGJH,EADF,CAAAC,cAAA,eAA4C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAClFF,EADkF,CAAAG,YAAA,EAAO,EACnF;IAGJH,EADF,CAAAC,cAAA,eAAuC,eACkD;IAErFD,EADA,CAAAwC,UAAA,KAAAC,0DAAA,iBAA6D,KAAAC,0DAAA,iBACC;IAGpE1C,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAhBJH,EAAA,CAAA2C,WAAA,aAAAlC,MAAA,CAAAU,eAAA,OAAAiB,UAAA,CAAgD;IAGfpC,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAkB,iBAAA,CAAAkB,UAAA,CAAAhB,UAAA,CAAwB;IAI7BpB,EAAA,CAAAa,SAAA,GAAsD;IAAtDb,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAwB,WAAA,OAAAf,MAAA,CAAAgB,aAAA,CAAAW,UAAA,uBAAsD;IAI1CpC,EAAA,CAAAa,SAAA,GAAgD;IAAhDb,EAAA,CAAA2C,WAAA,aAAAlC,MAAA,CAAAU,eAAA,OAAAiB,UAAA,CAAgD;IAC5DpC,EAAA,CAAAa,SAAA,EAAmC;IAAnCb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAU,eAAA,OAAAiB,UAAA,CAAmC;IAClCpC,EAAA,CAAAa,SAAA,EAAmC;IAAnCb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAU,eAAA,OAAAiB,UAAA,CAAmC;;;;;IAvChEpC,EAHJ,CAAAC,cAAA,cAAoG,cAEjE,aACA;IAC7BD,EAAA,CAAAI,SAAA,YAA0B;IAC1BJ,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,cAAkC,cACgB;IAC9CD,EAAA,CAAAI,SAAA,YAA0B;IAC1BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IACZF,EADY,CAAAG,YAAA,EAAO,EACb;IACNH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAI,SAAA,aAA8B;IAC9BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IACZF,EADY,CAAAG,YAAA,EAAO,EACb;IAEJH,EADF,CAAAC,cAAA,eAAsC,YAC9B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEZF,EAFY,CAAAG,YAAA,EAAO,EACX,EACF;IAGNH,EAAA,CAAAwC,UAAA,KAAAI,qDAAA,oBACoF;IAiBtF5C,EAAA,CAAAG,YAAA,EAAM;;;;IAtCAH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,aAAAL,MAAA,CAAAoC,iBAAA,GAAAC,MAAA,yBACF;IAmBmD9C,EAAA,CAAAa,SAAA,IAAwB;IAAxBb,EAAA,CAAAqB,UAAA,YAAAZ,MAAA,CAAAoC,iBAAA,GAAwB;;;;;IA6B7E7C,EADF,CAAAC,cAAA,cAAyD,aAC1B;IAC3BD,EAAA,CAAAI,SAAA,YAA2B;IAC3BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAElBF,EAFkB,CAAAG,YAAA,EAAO,EACjB,EACF;;;;;;IAMFH,EAHJ,CAAAC,cAAA,eAA6D,eAExB,cACL;IAC1BD,EAAA,CAAAI,SAAA,YAA4B;IAC5BJ,EAAA,CAAAE,MAAA,6CACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAqC,gCAC4C;IAA9CD,EAAA,CAAAK,UAAA,2BAAA0C,0FAAAC,MAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAiBF,MAAA,CAAAyC,mBAAA,CAAAF,MAAA,CAA2B;IAAA,EAAC;IAEhFhD,EADE,CAAAG,YAAA,EAAoB,EAChB;IAEJH,EADF,CAAAC,cAAA,eAA+B,kBAC8B;IAA3BD,EAAA,CAAAK,UAAA,mBAAA8C,uEAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,cAAA,EAAgB;IAAA,EAAC;IACxDpD,EAAA,CAAAI,SAAA,cAA6B;IAC7BJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAA+F;IAA/DD,EAAA,CAAAK,UAAA,mBAAAgD,wEAAA;MAAArD,EAAA,CAAAO,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6C,gBAAA,EAAkB;IAAA,EAAC;IAC1DtD,EAAA,CAAAI,SAAA,cAA2B;IAC3BJ,EAAA,CAAAE,MAAA,kCACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAN6DH,EAAA,CAAAa,SAAA,IAAiC;IAAjCb,EAAA,CAAAqB,UAAA,cAAAZ,MAAA,CAAA8C,iBAAA,GAAiC;;;;;IAuB5FvD,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvFH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAChC;;;;;IAFqBH,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAkB,iBAAA,GAAAsC,OAAA,GAAA/C,MAAA,CAAAU,eAAA,qBAAAqC,OAAA,CAAAC,iBAAA,kBAAAD,OAAA,CAAAC,iBAAA,CAAAX,MAAA,OAAuD;;;;;IAqC5E9C,EAJN,CAAAC,cAAA,eACiC,eACT,eACG,gBACG;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAEhDF,EAFgD,CAAAG,YAAA,EAAO,EAC/C,EACF;IAEJH,EADF,CAAAC,cAAA,eAAsB,gBACK;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAClDF,EADkD,CAAAG,YAAA,EAAO,EACnD;IAEJH,EADF,CAAAC,cAAA,eAAqB,gBACK;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IAEJH,EADF,CAAAC,cAAA,gBAAuB,iBACK;IAAAD,EAAA,CAAAE,MAAA,IAA0C;;IACtEF,EADsE,CAAAG,YAAA,EAAO,EACvE;IAEJH,EADF,CAAAC,cAAA,gBAAuB,iBACK;IAAAD,EAAA,CAAAE,MAAA,IAAyC;;IACrEF,EADqE,CAAAG,YAAA,EAAO,EACtE;IAEJH,EADF,CAAAC,cAAA,gBAAwB,iBACK;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAExDF,EAFwD,CAAAG,YAAA,EAAO,EACvD,EACF;;;;;IArBJH,EAAA,CAAA2C,WAAA,aAAAe,KAAA,WAA8B;IAGF1D,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAkB,iBAAA,CAAAyC,QAAA,CAAAC,SAAA,CAAoB;IAIrB5D,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAkB,iBAAA,CAAAyC,QAAA,CAAAE,KAAA,QAAuB;IAGxB7D,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAkB,iBAAA,CAAAyC,QAAA,CAAAG,MAAA,CAAiB;IAGf9D,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAAwB,WAAA,QAAAmC,QAAA,CAAAI,UAAA,eAA0C;IAG1C/D,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAAwB,WAAA,SAAAmC,QAAA,CAAAK,SAAA,eAAyC;IAGxChE,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAkB,iBAAA,CAAAyC,QAAA,CAAAM,OAAA,QAAyB;;;;;IA3DxDjE,EAPV,CAAAC,cAAA,eAAyD,eAGpB,cACP,cACI,eACS,aACP;IACxBD,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAwC,UAAA,IAAA0B,oDAAA,mBAA4E;IAMhFlE,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAyB,gBACG,gBACF;IACpBD,EAAA,CAAAI,SAAA,cAA4B;IAC5BJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAsB;IACpBD,EAAA,CAAAI,SAAA,cAAwB;IACxBJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAqB;IACnBD,EAAA,CAAAI,SAAA,cAAyB;IACzBJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACrBD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACrBD,EAAA,CAAAI,SAAA,aAA+B;IAC/BJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAI,SAAA,cAAmC;IACnCJ,EAAA,CAAAE,MAAA,sBACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAwC,UAAA,KAAA2B,qDAAA,qBACiC;IA0B3CnE,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;;;;;;IA9DoCH,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAqB,UAAA,UAAA+C,OAAA,GAAA3D,MAAA,CAAAU,eAAA,qBAAAiD,OAAA,CAAAX,iBAAA,CAA0C;IAmClCzD,EAAA,CAAAa,SAAA,IAAyC;IAAzCb,EAAA,CAAAqB,UAAA,aAAAmC,OAAA,GAAA/C,MAAA,CAAAU,eAAA,qBAAAqC,OAAA,CAAAC,iBAAA,CAAyC;;;;;IAqDnFzD,EAFF,CAAAC,cAAA,eAC4E,cAClD;IACtBD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAuF;IAC/FF,EAD+F,CAAAG,YAAA,EAAO,EAChG;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,MAAA,GAEF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IANIH,EAAA,CAAAa,SAAA,GAAuF;IAAvFb,EAAA,CAAAqE,kBAAA,KAAA5D,MAAA,CAAAU,eAAA,GAAAmD,UAAA,sCAAA7D,MAAA,CAAAU,eAAA,GAAAoD,aAAA,OAAuF;IAG7FvE,EAAA,CAAAa,SAAA,GAEF;IAFEb,EAAA,CAAAc,kBAAA,UAAAd,EAAA,CAAAwB,WAAA,OAAAf,MAAA,CAAA+D,iBAAA,CAAA/D,MAAA,CAAAU,eAAA,GAAAsC,iBAAA,EAAAhD,MAAA,CAAAU,eAAA,GAAAoD,aAAA,iBAEF;;;;;;IApSRvE,EAHJ,CAAAC,cAAA,UAA4C,cAEJ,cACA;IAElCD,EAAA,CAAAwC,UAAA,IAAAiC,6CAAA,oBAAsD;IAqCpDzE,EADF,CAAAC,cAAA,cAAgC,iBAC2C;IAAjCD,EAAA,CAAAK,UAAA,mBAAAqE,gEAAA;MAAA1E,EAAA,CAAAO,aAAA,CAAAoE,GAAA;MAAA,MAAAlE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmE,oBAAA,EAAsB;IAAA,EAAC;IACtE5E,EAAA,CAAAI,SAAA,YAA6B;IAC7BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACZF,EADY,CAAAG,YAAA,EAAO,EACV;IACTH,EAAA,CAAAC,cAAA,iBAAiE;IAAzBD,EAAA,CAAAK,UAAA,mBAAAwE,gEAAA;MAAA7E,EAAA,CAAAO,aAAA,CAAAoE,GAAA;MAAA,MAAAlE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqE,YAAA,EAAc;IAAA,EAAC;IAC9D9E,EAAA,CAAAI,SAAA,aAA2B;IAC3BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IACZF,EADY,CAAAG,YAAA,EAAO,EACV;IAMTH,EALA,CAAAwC,UAAA,KAAAuC,iDAAA,qBACyG,KAAAC,iDAAA,qBAKA;IAM/GhF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,4BAAuF;IAArED,EAAA,CAAAiF,gBAAA,2BAAAC,mFAAAlC,MAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAAoE,GAAA;MAAA,MAAAlE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAmF,kBAAA,CAAA1E,MAAA,CAAA2E,kBAAA,EAAApC,MAAA,MAAAvC,MAAA,CAAA2E,kBAAA,GAAApC,MAAA;MAAA,OAAAhD,EAAA,CAAAW,WAAA,CAAAqC,MAAA;IAAA,EAAgC;IAEhDhD,EAAA,CAAAC,cAAA,eAAoC;IA8BlCD,EA5BA,CAAAwC,UAAA,KAAA6C,8CAAA,kBAAgD,KAAAC,8CAAA,kBAQN,KAAAC,8CAAA,kBAYqD,KAAAC,8CAAA,mBAQK;IAgDxGxF,EADE,CAAAG,YAAA,EAAM,EACW;IAGnBH,EAAA,CAAAC,cAAA,4BACwF;IADtED,EAAA,CAAAiF,gBAAA,2BAAAQ,mFAAAzC,MAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAAoE,GAAA;MAAA,MAAAlE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAmF,kBAAA,CAAA1E,MAAA,CAAAiF,mBAAA,EAAA1C,MAAA,MAAAvC,MAAA,CAAAiF,mBAAA,GAAA1C,MAAA;MAAA,OAAAhD,EAAA,CAAAW,WAAA,CAAAqC,MAAA;IAAA,EAAiC;IAGjDhD,EAAA,CAAAC,cAAA,eAAsC;IAUpCD,EARA,CAAAwC,UAAA,KAAAmD,8CAAA,kBAAyD,KAAAC,8CAAA,mBAQI;IAwBjE5F,EADE,CAAAG,YAAA,EAAM,EACW;IAGnBH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAwC,UAAA,KAAAqD,8CAAA,mBAAyD;IA+EnD7F,EAHN,CAAAC,cAAA,eAAyC,eACb,eACI,cACA;IACxBD,EAAA,CAAAI,SAAA,aAA+B;IAC/BJ,EAAA,CAAAE,MAAA,kCACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACD;IAIFH,EAHJ,CAAAC,cAAA,eAA+B,eAEM,eACT;IACtBD,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IACVF,EADU,CAAAG,YAAA,EAAO,EACX;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAwC,UAAA,KAAAsD,8CAAA,kBAC4E;IAa1E9F,EADF,CAAAC,cAAA,eAAgC,eACN;IACtBD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IACVF,EADU,CAAAG,YAAA,EAAO,EACX;IACNH,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAE,MAAA,IACF;;IAIRF,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,eAAmC,eACP,eACI,cACA;IACxBD,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAE,MAAA,kCACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACD;IAGFH,EAFJ,CAAAC,cAAA,eAA2B,eACE,cACO;IAC9BD,EAAA,CAAAI,SAAA,aAA0B;IAC1BJ,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,gEAAW;IAEvCF,EAFuC,CAAAG,YAAA,EAAO,EACtC,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,cACO;IAC9BD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,cACO;IAC9BD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,kFAAc;IAE1CF,EAF0C,CAAAG,YAAA,EAAO,EACzC,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,cACO;IAC9BD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,0EAAW;IAOjDF,EAPiD,CAAAG,YAAA,EAAO,EACtC,EACF,EACF,EACF,EACF,EACF,EACF;;;;IAxW6BH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAU,eAAA,GAAuB;IA8C/CnB,EAAA,CAAAa,SAAA,IAAoG;IAApGb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAU,eAAA,MAAAV,MAAA,CAAAsF,oBAAA,CAAAtF,MAAA,CAAAU,eAAA,GAAA6E,gBAAA,kBAAoG;IAKpGhG,EAAA,CAAAa,SAAA,EAAoG;IAApGb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAU,eAAA,MAAAV,MAAA,CAAAsF,oBAAA,CAAAtF,MAAA,CAAAU,eAAA,GAAA6E,gBAAA,kBAAoG;IAS3FhG,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAiG,gBAAA,YAAAxF,MAAA,CAAA2E,kBAAA,CAAgC;IAACpF,EAAA,CAAAqB,UAAA,aAAAZ,MAAA,CAAAyF,sBAAA,CAAmC;IAI5ElG,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAA0F,SAAA,GAAiB;IAQjBnG,EAAA,CAAAa,SAAA,EAAa;IAAbb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAM,KAAA,GAAa;IAYbf,EAAA,CAAAa,SAAA,EAAkE;IAAlEb,EAAA,CAAAqB,UAAA,UAAAZ,MAAA,CAAA0F,SAAA,OAAA1F,MAAA,CAAAM,KAAA,MAAAN,MAAA,CAAAoC,iBAAA,GAAAC,MAAA,OAAkE;IAQlE9C,EAAA,CAAAa,SAAA,EAAgE;IAAhEb,EAAA,CAAAqB,UAAA,UAAAZ,MAAA,CAAA0F,SAAA,OAAA1F,MAAA,CAAAM,KAAA,MAAAN,MAAA,CAAAoC,iBAAA,GAAAC,MAAA,KAAgE;IAmDxD9C,EAAA,CAAAa,SAAA,EAAiC;IAAjCb,EAAA,CAAAiG,gBAAA,YAAAxF,MAAA,CAAAiF,mBAAA,CAAiC;IACjD1F,EAAA,CAAAqB,UAAA,aAAArB,EAAA,CAAAoG,eAAA,KAAAC,GAAA,EAAqF;IAI7ErG,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAA6F,kBAAA,GAA0B;IAQ1BtG,EAAA,CAAAa,SAAA,EAA2B;IAA3Bb,EAAA,CAAAqB,UAAA,UAAAZ,MAAA,CAAA6F,kBAAA,GAA2B;IA4BHtG,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAU,eAAA,GAAuB;IA4F7CnB,EAAA,CAAAa,SAAA,IACF;IADEb,EAAA,CAAAc,kBAAA,UAAAd,EAAA,CAAAwB,WAAA,SAAAf,MAAA,CAAA8F,iBAAA,CAAA9F,MAAA,CAAAU,eAAA,GAAAsC,iBAAA,iBACF;IAKCzD,EAAA,CAAAa,SAAA,GAAuE;IAAvEb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAU,eAAA,GAAAqF,UAAA,IAAA/F,MAAA,CAAAU,eAAA,GAAAoD,aAAA,CAAuE;IAkBtEvE,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,UAAAd,EAAA,CAAAwB,WAAA,SAAAf,MAAA,CAAAgG,uBAAA,CAAAhG,MAAA,CAAAU,eAAA,mBACF;;;;;IAzUZnB,EAHF,CAAAC,cAAA,UAAsC,cAGZ;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAuBnCH,EApBA,CAAAwC,UAAA,IAAAkE,uCAAA,mBAAkE,IAAAC,uCAAA,mBAoBtB;IA+W9C3G,EAAA,CAAAG,YAAA,EAAM;;;;IAnYEH,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAoC,iBAAA,GAAAC,MAAA,OAAsC;IAoBtC9C,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAoC,iBAAA,GAAAC,MAAA,KAAoC;;;ADrBhD,OAAM,MAAO8D,kBAAkB;EAyB7BC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAtBpC,KAAAjE,iBAAiB,GAAGpD,MAAM,CAAyB,EAAE,CAAC;IACtD,KAAA0B,eAAe,GAAG1B,MAAM,CAA8B,IAAI,CAAC;IAC3D,KAAA2F,kBAAkB,GAAG3F,MAAM,CAAC,KAAK,CAAC;IAClC,KAAA0G,SAAS,GAAG1G,MAAM,CAAC,KAAK,CAAC;IACzB,KAAAsB,KAAK,GAAGtB,MAAM,CAAgB,IAAI,CAAC;IAEnC;IACA,KAAAiG,mBAAmB,GAAGjG,MAAM,CAAC,KAAK,CAAC;IACnC,KAAA6G,kBAAkB,GAAG7G,MAAM,CAAC,KAAK,CAAC;IAClC,KAAA8D,iBAAiB,GAAG9D,MAAM,CAAC,KAAK,CAAC;IACjC,KAAAsH,aAAa,GAAGtH,MAAM,CAAgB,IAAI,CAAC;IAE3C;IACA,KAAAyG,sBAAsB,GAAkB;MACtCc,KAAK,EAAE,gBAAgB;MACvBC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE,EAAE;MACpBC,eAAe,EAAE;KAClB;EAEyD;EAE1DC,QAAQA,CAAA;IACN,IAAI,CAAC1G,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACuF,SAAS,CAACoB,GAAG,CAAC,IAAI,CAAC;IACxB,IAAI,CAACxG,KAAK,CAACwG,GAAG,CAAC,IAAI,CAAC;IAEpB,IAAI,CAACT,gBAAgB,CAACU,wCAAwC,EAAE,CAC7DC,SAAS,CAAC;MACTC,IAAI,EAAGC,QAA8C,IAAI;QACvD,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC/E,iBAAiB,CAAC0E,GAAG,CAACI,QAAQ,CAACC,OAAO,CAAC;UAC5C,IAAID,QAAQ,CAACC,OAAO,CAAC9E,MAAM,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAC3B,eAAe,CAACoG,GAAG,CAACI,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;SAElD,MAAM;UACL,IAAI,CAAC/E,iBAAiB,CAAC0E,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;;QAElC,IAAI,CAACpB,SAAS,CAACoB,GAAG,CAAC,KAAK,CAAC;MAC3B,CAAC;MACDxG,KAAK,EAAGA,KAAK,IAAI;QACf8G,OAAO,CAAC9G,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACA,KAAK,CAACwG,GAAG,CAAC,iBAAiB,CAAC;QACjC,IAAI,CAAC1E,iBAAiB,CAAC0E,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAChC,IAAI,CAACpG,eAAe,CAACoG,GAAG,CAAC,IAAI,CAAC;QAC9B,IAAI,CAACpB,SAAS,CAACoB,GAAG,CAAC,KAAK,CAAC;MAC3B;KACD,CAAC;EACN;EACAhF,aAAaA,CAACuF,OAA6B;IACzC,IAAI,CAAC3G,eAAe,CAACoG,GAAG,CAACO,OAAO,CAAC;IACjC,IAAI,CAAC1C,kBAAkB,CAACmC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;EACtC;EAEA3C,oBAAoBA,CAAA;IAClB,IAAI,CAACQ,kBAAkB,CAACmC,GAAG,CAAC,CAAC,IAAI,CAACnC,kBAAkB,EAAE,CAAC;EACzD;EAEA2C,qBAAqBA,CAAA;IACnB,IAAI,CAAC3C,kBAAkB,CAACmC,GAAG,CAAC,KAAK,CAAC;EACpC;EAEA;EACAjG,cAAcA,CAACwG,OAA6B;IAC1C,MAAME,MAAM,GAAG,IAAI,CAACjC,oBAAoB,CAAC+B,OAAO,CAAC9B,gBAAgB,CAAC;IAClE,QAAQgC,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC,KAAK,QAAQ;QAAE,OAAO,eAAe;MACrC,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,MAAM;QAAE,OAAO,aAAa;MACjC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC;QAAS,OAAO,gBAAgB;;EAEpC;EAEA;EACAC,aAAaA,CAACH,OAA6B;IACzC,MAAME,MAAM,GAAG,IAAI,CAACjC,oBAAoB,CAAC+B,OAAO,CAAC9B,gBAAgB,CAAC;IAClE,QAAQgC,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,YAAY;MACnC,KAAK,QAAQ;QAAE,OAAO,gBAAgB;MACtC,KAAK,WAAW;QAAE,OAAO,mBAAmB;MAC5C,KAAK,MAAM;QAAE,OAAO,WAAW;MAC/B,KAAK,UAAU;QAAE,OAAO,qBAAqB;MAC7C,KAAK,UAAU;QAAE,OAAO,eAAe;MACvC,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C;QAAS,OAAO,YAAY;;EAEhC;EAEA;EACAzG,aAAaA,CAACuG,OAA6B;IACzC,MAAME,MAAM,GAAG,IAAI,CAACjC,oBAAoB,CAAC+B,OAAO,CAAC9B,gBAAgB,CAAC;IAClE,QAAQgC,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,KAAK;MAC5B,KAAK,QAAQ;QAAE,OAAO,KAAK;MAC3B,KAAK,WAAW;QAAE,OAAO,KAAK;MAC9B,KAAK,MAAM;QAAE,OAAO,KAAK;MACzB,KAAK,UAAU;QAAE,OAAO,KAAK;MAC7B,KAAK,UAAU;QAAE,OAAO,KAAK;MAC7B,KAAK,SAAS;QAAE,OAAO,KAAK;MAC5B;QAAS,OAAO,KAAK;;EAEzB;EAEAlD,YAAYA,CAAA;IACV;IACA,MAAMoD,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,sBAAsB,CAAC;IACrE,IAAIF,WAAW,IAAI,IAAI,CAAC/G,eAAe,EAAE,EAAE;MACzC,MAAMkH,eAAe,GAAG,IAAI,CAACC,qBAAqB,EAAE;MACpDJ,WAAW,CAACK,QAAQ,CAACC,KAAK,CAACH,eAAe,CAAC;MAC3CH,WAAW,CAACK,QAAQ,CAACE,KAAK,EAAE;MAE5B;MACAP,WAAW,CAACQ,MAAM,GAAG,MAAK;QACxBR,WAAW,CAACS,KAAK,EAAE;QACnBT,WAAW,CAACO,KAAK,EAAE;MACrB,CAAC;;EAEL;EAEQH,qBAAqBA,CAAA;IAC3B,MAAMR,OAAO,GAAG,IAAI,CAAC3G,eAAe,EAAE;IACtC,IAAI,CAAC2G,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,IAAIc,QAAQ,GAAG7I,kBAAkB;IAEjC;IACA,MAAM8I,KAAK,GAAGf,OAAO,CAACrE,iBAAiB,IAAI,EAAE;IAC7C,MAAMqF,QAAQ,GAAG,IAAI,CAACvC,iBAAiB,CAACsC,KAAK,CAAC;IAC9C,MAAME,QAAQ,GAAGjB,OAAO,CAACtB,UAAU,GAAG,IAAI,CAAChC,iBAAiB,CAACqE,KAAK,EAAEf,OAAO,CAACvD,aAAa,CAAC,GAAG,CAAC;IAC9F,MAAMyE,WAAW,GAAG,IAAI,CAACvC,uBAAuB,CAACqB,OAAO,CAAC;IAEzD;IACA,MAAMmB,SAAS,GAAGJ,KAAK,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;;kCAEfA,KAAK,GAAG,CAAC;cAC7BD,IAAI,CAACvF,SAAS,IAAI,EAAE;qCACG,CAACuF,IAAI,CAACpF,UAAU,IAAI,CAAC,EAAEsF,cAAc,EAAE;kCAC1CF,IAAI,CAACtF,KAAK,IAAI,EAAE;kCAChBsF,IAAI,CAACrF,MAAM,IAAI,CAAC;qCACb,CAACqF,IAAI,CAACnF,SAAS,IAAI,CAAC,EAAEqF,cAAc,EAAE;cAC7DF,IAAI,CAAClF,OAAO,IAAI,EAAE;;KAE3B,CAAC,CAACqF,IAAI,CAAC,EAAE,CAAC;IAEX;IACA,MAAMC,iBAAiB,GAAGzB,OAAO,CAACtB,UAAU,IAAIsB,OAAO,CAACvD,aAAa,GACnE,+BAA+BuD,OAAO,CAACxD,UAAU,IAAI,MAAM,KAAKwD,OAAO,CAACvD,aAAa,UAAUwE,QAAQ,CAACM,cAAc,EAAE,QAAQ,GAAG,EAAE;IAEvI;IACAT,QAAQ,GAAGA,QAAQ,CAChBY,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;IAAA,CACtCA,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IAAA,CAClCA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,gBAAgB,EAAE,IAAIC,IAAI,EAAE,CAACC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CACjEF,OAAO,CAAC,gBAAgB,EAAEP,SAAS,CAAC,CACpCO,OAAO,CAAC,qBAAqB,EAAE,OAAOV,QAAQ,CAACO,cAAc,EAAE,EAAE,CAAC,CAClEG,OAAO,CAAC,wBAAwB,EAAED,iBAAiB,CAAC,CACpDC,OAAO,CAAC,kBAAkB,EAAE,OAAOR,WAAW,CAACK,cAAc,EAAE,EAAE,CAAC,CAClEG,OAAO,CAAC,oBAAoB,EAAE,IAAIC,IAAI,EAAE,CAACJ,cAAc,CAAC,OAAO,CAAC,CAAC;IAEpE,OAAOT,QAAQ;EACjB;EAMAe,kBAAkBA,CAAC7B,OAA6B;IAC9C,IAAI,CAAC3G,eAAe,CAACoG,GAAG,CAACO,OAAO,CAAC;IACjC;IACA,MAAM8B,cAAc,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,oBAAoB,CAAC;IACnE,IAAID,cAAc,EAAE;MAClBA,cAAc,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAO,CAAE,CAAC;;EAEzE;EAEAC,cAAcA,CAACnC,OAA6B;IAC1C;IACAoC,KAAK,CAAC,QAAQpC,OAAO,CAAC1G,UAAU,UAAU,IAAI,CAACD,eAAe,EAAE,EAAEC,UAAU,YAAY,CAAC;EAC3F;EAEA+I,eAAeA,CAAA;IACb;IACAD,KAAK,CAAC,cAAc,CAAC;EACvB;EAEAlI,YAAYA,CAAA;IACV;IACAkI,KAAK,CAAC,cAAc,CAAC;EACvB;EAEAnE,oBAAoBA,CAACqE,UAAmB;IACtC,QAAQA,UAAU;MAChB,KAAK,CAAC;QAAE,OAAO,SAAS;MAAG;MAC3B,KAAK,CAAC;QAAE,OAAO,QAAQ;MAAI;MAC3B,KAAK,CAAC;QAAE,OAAO,WAAW;MAAE;MAC5B;QAAS,OAAO,SAAS;MAAE;;EAE/B;EAEA;EACAC,kBAAkBA,CAACvC,OAA6B;IAC9C,OAAO,IAAIA,OAAO,CAACwC,mBAAmB,EAAE;EAC1C;EAEA;EACAC,gBAAgBA,CAACzC,OAA6B;IAC5C,OAAOA,OAAO,CAAC1G,UAAU,IAAI,MAAM;EACrC;EAEA;EACAK,aAAaA,CAACqG,OAA6B;IACzC,OAAOA,OAAO,CAAC0C,SAAS,GAAG,IAAIf,IAAI,CAAC3B,OAAO,CAAC0C,SAAS,CAAC,GAAG,IAAIf,IAAI,EAAE;EACrE;EAEA;EACAlD,iBAAiBA,CAACsC,KAAiC;IACjD,IAAI,CAACA,KAAK,EAAE,OAAO,CAAC;IACpB,OAAOA,KAAK,CAAC4B,MAAM,CAAC,CAACC,GAAG,EAAEvB,IAAI,KAAKuB,GAAG,IAAIvB,IAAI,CAACnF,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACpE;EAEA;EACA2G,YAAYA,CAAC9B,KAAiC;IAC5C,MAAMC,QAAQ,GAAG,IAAI,CAACvC,iBAAiB,CAACsC,KAAK,CAAC;IAC9C,OAAO+B,IAAI,CAACC,KAAK,CAAC/B,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC;EACrC;EAEA;EACAtE,iBAAiBA,CAACqE,KAAiC,EAAEiC,YAAqB;IACxE,IAAI,CAACA,YAAY,IAAIA,YAAY,IAAI,CAAC,EAAE,OAAO,CAAC;IAChD,MAAMhC,QAAQ,GAAG,IAAI,CAACvC,iBAAiB,CAACsC,KAAK,CAAC;IAC9C,OAAO+B,IAAI,CAACC,KAAK,CAAC/B,QAAQ,IAAIgC,YAAY,GAAG,GAAG,CAAC,CAAC;EACpD;EAEA;EACArE,uBAAuBA,CAACqB,OAA6B;IACnD,MAAMgB,QAAQ,GAAG,IAAI,CAACvC,iBAAiB,CAACuB,OAAO,CAACrE,iBAAiB,CAAC;IAClE,MAAMsF,QAAQ,GAAGjB,OAAO,CAACtB,UAAU,GAAG,IAAI,CAAChC,iBAAiB,CAACsD,OAAO,CAACrE,iBAAiB,EAAEqE,OAAO,CAACvD,aAAa,CAAC,GAAG,CAAC;IAClH,OAAOuE,QAAQ,GAAGC,QAAQ;EAC5B;EAEA;EAEA;EACAlH,mBAAmBA,CAAA;IACjBgG,OAAO,CAACkD,GAAG,CAAC,SAAS,CAAC;IACtBlD,OAAO,CAACkD,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC5J,eAAe,EAAE,CAAC;IAC5C0G,OAAO,CAACkD,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC5J,eAAe,EAAE,EAAE6E,gBAAgB,CAAC;IAC9D6B,OAAO,CAACkD,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAChF,oBAAoB,CAAC,IAAI,CAAC5E,eAAe,EAAE,EAAE6E,gBAAgB,CAAC,CAAC;IAE1F;IACA,IAAI,CAAC,IAAI,CAAC7E,eAAe,EAAE,EAAE;MAC3B+I,KAAK,CAAC,YAAY,CAAC;MACnB;;IAGF;IACA,MAAMc,aAAa,GAAG,IAAI,CAACjF,oBAAoB,CAAC,IAAI,CAAC5E,eAAe,EAAE,EAAE6E,gBAAgB,CAAC;IACzF,IAAIgF,aAAa,KAAK,WAAW,EAAE;MACjCd,KAAK,CAAC,YAAY,CAAC;MACnB;;IAGF,IAAI,CAACxE,mBAAmB,CAAC6B,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,CAAChE,iBAAiB,CAACgE,GAAG,CAAC,KAAK,CAAC;IACjC,IAAI,CAACR,aAAa,CAACQ,GAAG,CAAC,IAAI,CAAC;EAC9B;EAEA;EACArE,mBAAmBA,CAAC+H,gBAA+B;IACjDpD,OAAO,CAACkD,GAAG,CAAC,SAAS,EAAEE,gBAAgB,EAAEC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;IAElE,IAAI,CAACnE,aAAa,CAACQ,GAAG,CAAC0D,gBAAgB,CAAC;IACxC,IAAI,CAAC1H,iBAAiB,CAACgE,GAAG,CAAC,CAAC,CAAC0D,gBAAgB,IAAIA,gBAAgB,CAACnI,MAAM,GAAG,CAAC,CAAC;IAE7E;IACA,IAAImI,gBAAgB,IAAI,CAACA,gBAAgB,CAACE,UAAU,CAAC,aAAa,CAAC,EAAE;MACnEtD,OAAO,CAACuD,IAAI,CAAC,cAAc,EAAEH,gBAAgB,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;EAEpE;EAEA;EACA9H,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACiI,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,KAAK,EAAE;MACzB,IAAI,CAAC/H,iBAAiB,CAACgE,GAAG,CAAC,KAAK,CAAC;MACjC,IAAI,CAACR,aAAa,CAACQ,GAAG,CAAC,IAAI,CAAC;;EAEhC;EAEA;EACAjE,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAAC,IAAI,CAACpC,eAAe,EAAE,EAAE;MACxD;;IAGF,IAAI,CAACmF,kBAAkB,CAACiB,GAAG,CAAC,IAAI,CAAC;IAEjC;IACA,MAAMgE,gBAAgB,GAAkB;MACtCC,mBAAmB,EAAE,IAAI,CAACrK,eAAe,EAAG,CAACmJ,mBAAmB;MAChEmB,KAAK,EAAE,IAAI,CAAC1E,aAAa;KAC1B;IAED;IACA,IAAI,CAACD,gBAAgB,CAAC4E,kCAAkC,CAAC;MAAEC,IAAI,EAAEJ;IAAgB,CAAE,CAAC,CACjF9D,SAAS,CAAC;MACTC,IAAI,EAAGC,QAA4B,IAAI;QACrCE,OAAO,CAACkD,GAAG,CAAC,YAAY,EAAEpD,QAAQ,CAAC;QAEnC;QACA,IAAIA,QAAQ,CAACiE,UAAU,KAAKhM,cAAc,CAACiM,EAAE,EAAE;UAC7C,IAAI,CAACC,sBAAsB,EAAE;SAC9B,MAAM;UACL,MAAMC,YAAY,GAAGpE,QAAQ,CAACqE,OAAO,IAAI,cAAcrE,QAAQ,CAACiE,UAAU,GAAG;UAC7E,IAAI,CAACK,oBAAoB,CAACF,YAAY,CAAC;;MAE3C,CAAC;MACDhL,KAAK,EAAGA,KAAK,IAAI;QACf8G,OAAO,CAAC9G,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClC,IAAI,CAACkL,oBAAoB,CAAClL,KAAK,CAAC;MAClC;KACD,CAAC;EACN;EAEA;EACQ+K,sBAAsBA,CAAA;IAC5B,IAAI,CAACxF,kBAAkB,CAACiB,GAAG,CAAC,KAAK,CAAC;IAClC,IAAI,CAAC7B,mBAAmB,CAAC6B,GAAG,CAAC,KAAK,CAAC;IAEnC;IACA,MAAM2E,cAAc,GAAG,IAAI,CAAC/K,eAAe,EAAE;IAC7C,IAAI+K,cAAc,EAAE;MAClB;MACAA,cAAc,CAAClG,gBAAgB,GAAG,CAAC,CAAC,CAAC;MACrC,IAAI,CAAC7E,eAAe,CAACoG,GAAG,CAAC;QAAE,GAAG2E;MAAc,CAAE,CAAC;;IAGjD;IACA,IAAI,CAACtL,oBAAoB,EAAE;IAE3B;IACAsJ,KAAK,CAAC,cAAc,CAAC;EACvB;EAEA;EACQ+B,oBAAoBA,CAAClL,KAAU;IACrC,IAAI,CAACuF,kBAAkB,CAACiB,GAAG,CAAC,KAAK,CAAC;IAClCM,OAAO,CAAC9G,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAE7B,IAAIgL,YAAY,GAAG,aAAa;IAEhC;IACA,IAAI,OAAOhL,KAAK,KAAK,QAAQ,EAAE;MAC7BgL,YAAY,GAAGhL,KAAK;KACrB,MAAM,IAAIA,KAAK,EAAEoL,OAAO,EAAE;MACzBJ,YAAY,GAAGhL,KAAK,CAACoL,OAAO;KAC7B,MAAM,IAAIpL,KAAK,EAAEA,KAAK,EAAEoL,OAAO,EAAE;MAChCJ,YAAY,GAAGhL,KAAK,CAACA,KAAK,CAACoL,OAAO;;IAGpCjC,KAAK,CAAC6B,YAAY,CAAC;EACrB;EAAC,QAAAK,CAAA,G;qBAvXUxF,kBAAkB,EAAA5G,EAAA,CAAAqM,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB5F,kBAAkB;IAAA6F,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCtB7B5M,EADF,CAAAC,cAAA,aAAqB,aACE;QAiBnBD,EAdA,CAAAwC,UAAA,IAAAsK,iCAAA,iBAAmD,IAAAC,iCAAA,iBAKN,IAAAC,iCAAA,iBASP;QA4Y1ChN,EADE,CAAAG,YAAA,EAAM,EACF;;;QA1ZIH,EAAA,CAAAa,SAAA,GAAiB;QAAjBb,EAAA,CAAAqB,UAAA,SAAAwL,GAAA,CAAA1G,SAAA,GAAiB;QAKjBnG,EAAA,CAAAa,SAAA,EAAa;QAAbb,EAAA,CAAAqB,UAAA,SAAAwL,GAAA,CAAA9L,KAAA,GAAa;QASbf,EAAA,CAAAa,SAAA,EAA8B;QAA9Bb,EAAA,CAAAqB,UAAA,UAAAwL,GAAA,CAAA1G,SAAA,OAAA0G,GAAA,CAAA9L,KAAA,GAA8B;;;mBDA5BrB,YAAY,EAAAuN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EAAAJ,EAAA,CAAAK,QAAA,EAAE3N,WAAW,EAAEE,oBAAoB,EAAEC,qBAAqB;IAAAyN,MAAA;IAAAC,eAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}